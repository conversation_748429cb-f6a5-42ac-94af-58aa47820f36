<?xml version="1.0" encoding="UTF-8"?>
<!--
// Copyright (C) 2016 The Qt Company Ltd.
// SPDX-License-Identifier: LicenseRef-Qt-Commercial OR LGPL-3.0-only OR GPL-2.0-only OR GPL-3.0-only
-->
<typesystem package="PySide6.QtWebSockets"
            namespace-begin="QT_BEGIN_NAMESPACE" namespace-end="QT_END_NAMESPACE">
  <load-typesystem name="typesystem_core.xml" generate="no"/>
  <load-typesystem name="typesystem_network.xml" generate="no"/>

  <object-type name="QMaskGenerator"/>

  <object-type name="QWebSocket">
    <extra-includes>
      <include file-name="QTcpSocket" location="global"/>
    </extra-includes>
  </object-type>

  <object-type name="QWebSocketCorsAuthenticator"/>

  <value-type name="QWebSocketHandshakeOptions" since="6.4"/>

  <namespace-type name="QWebSocketProtocol">
    <enum-type name="Version"/>
    <enum-type name="CloseCode"/>
  </namespace-type>

  <object-type name="QWebSocketServer">
    <enum-type name="SslMode"/>
    <extra-includes>
      <include file-name="QWebSocketCorsAuthenticator" location="global"/>
    </extra-includes>
  </object-type>
</typesystem>
