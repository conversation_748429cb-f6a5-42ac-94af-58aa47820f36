<?xml version="1.0" encoding="UTF-8"?>
<!--
// Copyright (C) 2017 The Qt Company Ltd.
// SPDX-License-Identifier: LicenseRef-Qt-Commercial OR LGPL-3.0-only OR GPL-2.0-only OR GPL-3.0-only
-->
<typesystem package="PySide6.QtDataVisualization" doc-mode="flat"
            namespace-begin="QT_BEGIN_NAMESPACE" namespace-end="QT_END_NAMESPACE">

   <extra-includes>
      <include file-name="qtdatavisualization_helper.h" location="global"/>
   </extra-includes>

  <load-typesystem name="datavisualization_common.xml" generate="no" />
  <load-typesystem name="typesystem_gui.xml" generate="no" />

  <function signature="qDefaultSurfaceFormat(bool)"/>

  <primitive-type name="QBarDataArray">
    <include file-name="qbardataproxy.h" location="global"/>
    <conversion-rule>
        <native-to-target>
            <insert-template name="cppqlistofptrtoqlists_to_py_conversion">
              <replace from="%INTYPE_0" to="QBarDataItem"/>
            </insert-template>
        </native-to-target>
        <target-to-native>
            <add-conversion type="PySequence">
                <insert-template name="py_to_cppqlistofptrtoqlists_conversion">
                    <replace from="%OUTTYPE_0" to="QBarDataItem"/>
                </insert-template>
            </add-conversion>
        </target-to-native>
    </conversion-rule>
  </primitive-type>
  <primitive-type name="QSurfaceDataArray">
    <include file-name="qsurfacedataproxy.h" location="global"/>
    <conversion-rule>
        <native-to-target>
            <insert-template name="cppqlistofptrtoqlists_to_py_conversion">
              <replace from="%INTYPE_0" to="QSurfaceDataItem"/>
            </insert-template>
        </native-to-target>
        <target-to-native>
            <add-conversion type="PySequence">
                <insert-template name="py_to_cppqlistofptrtoqlists_conversion">
                    <replace from="%OUTTYPE_0" to="QSurfaceDataItem"/>
                </insert-template>
            </add-conversion>
        </target-to-native>
    </conversion-rule>
  </primitive-type>

  <object-type name="QAbstract3DAxis">
    <enum-type name="AxisOrientation"/>
    <enum-type name="AxisType"/>
  </object-type>
  <object-type name="QCategory3DAxis"/>
  <object-type name="QLogValue3DAxisFormatter"/>
  <object-type name="QValue3DAxis">
  <modify-function signature="setFormatter(QValue3DAxisFormatter *)">
    <modify-argument index="1">
      <parent index="this" action="add"/>
    </modify-argument>
  </modify-function>
  </object-type>
  <object-type name="QValue3DAxisFormatter">
    <inject-code class="native" position="beginning" file="../glue/qtdatavisualization.cpp"
                 snippet="qvalue3daxisformatter-friend"/>
    <modify-function signature="createNewInstance() const">
      <modify-argument index="return">
        <define-ownership class="native" owner="c++"/>
        <define-ownership class="target" owner="default"/>
      </modify-argument>
    </modify-function>
    <modify-function signature="populateCopy(QValue3DAxisFormatter&amp;)const">
      <modify-argument index="1">
        <parent index="this" action="add"/>
      </modify-argument>
    </modify-function>
    <!-- PYSIDE-2025: gridPositions(), labelPositions(), labelStrings() return
         non-const-references to lists for modifications. Add setters for them. -->
    <add-function signature="setGridPositions(const QList&lt;float&gt;&amp;@grid_positions@)">
      <inject-documentation format="target" mode="append">
      Sets the normalized grid line positions to ``grid_positions``.
      </inject-documentation>
      <inject-code class="target" position="beginning" file="../glue/qtdatavisualization.cpp"
                   snippet="qvalue3daxisformatter-setgridpositions"/>
    </add-function>
    <add-function signature="setLabelPositions(const QList&lt;float&gt;&amp;@label_positions@)">
      <inject-documentation format="target" mode="append">
      Sets the normalized label positions to ``label_positions``.
      </inject-documentation>
      <inject-code class="target" position="beginning" file="../glue/qtdatavisualization.cpp"
                   snippet="qvalue3daxisformatter-setlabelpositions"/>
    </add-function>
    <add-function signature="setLabelStrings(const QStringList&amp;@label_strings@)">
      <inject-documentation format="target" mode="append">
      Sets the label strings to ``label_strings``.
      </inject-documentation>
      <inject-code class="target" position="beginning" file="../glue/qtdatavisualization.cpp"
                   snippet="qvalue3daxisformatter-setlabelstrings"/>
    </add-function>
  </object-type>
  <object-type name="QAbstract3DSeries">
    <enum-type name="Mesh"/>
    <enum-type name="SeriesType"/>
  </object-type>
  <object-type name="QAbstractDataProxy">
    <enum-type name="DataType"/>
  </object-type>
  <object-type name="QBar3DSeries">
    <modify-function signature="setDataProxy(QBarDataProxy*)">
      <modify-argument index="1">
        <parent index="this" action="add"/>
      </modify-argument>
    </modify-function>
  </object-type>
  <value-type name="QBarDataItem"/>
  <object-type name="QBarDataProxy">
    <modify-function signature="resetArray(QBarDataArray*)" remove="all"/>
    <add-function signature="resetArray(const QBarDataArray&amp;)">
      <inject-code class="target" position="beginning" file="../glue/qtdatavisualization.cpp"
                   snippet="dataproxy-resetarray"/>
    </add-function>
    <modify-function signature="resetArray(QBarDataArray*,const QStringList&amp;,const QStringList&amp;)"
                     remove="all"/>
    <add-function signature="resetArray(const QBarDataArray&amp;,const QStringList&amp;,const QStringList&amp;)">
      <inject-code class="target" position="beginning" file="../glue/qtdatavisualization.cpp"
                   snippet="dataproxy-resetarray2"/>
    </add-function>
    <modify-function signature="resetArray(QBarDataArray*,const QStringList&amp;,const QStringList&amp;)">
      <modify-argument index="1">
        <parent index="this" action="add"/>
      </modify-argument>
    </modify-function>

    <!-- PYSIDE-1438: Replace all add/set/insertRow() taking a 'QList*' by overloads
         taking 'const QList &' since an allocated list needs to be passed. -->
    <modify-function signature="addRow(QList&lt;QBarDataItem&gt;*)" remove="all"/>
    <add-function signature="addRow(const QList&lt;QBarDataItem&gt;&amp;)" return-type="int">
        <inject-code class="target" position="beginning" file="../glue/qtdatavisualization.cpp"
                     snippet="dataproxy-addrow"/>
    </add-function>
    <modify-function signature="addRow(QList&lt;QBarDataItem&gt;*,const QString&amp;)" remove="all"/>
    <add-function signature="addRow(const QList&lt;QBarDataItem&gt;&amp;,const QString&amp;)"
                  return-type="int">
        <inject-code class="target" position="beginning" file="../glue/qtdatavisualization.cpp"
                     snippet="dataproxy-addrow-string"/>
    </add-function>

    <modify-function signature="insertRow(int,QList&lt;QBarDataItem&gt;*)" remove="all"/>
    <add-function signature="insertRow(int,const QList&lt;QBarDataItem&gt;&amp;)">
        <inject-code class="target" position="beginning" file="../glue/qtdatavisualization.cpp"
                     snippet="dataproxy-insertrow"/>
    </add-function>
    <modify-function signature="insertRow(int,QList&lt;QBarDataItem&gt;*,const QString&amp;)" remove="all"/>
    <add-function signature="insertRow(int,const QList&lt;QBarDataItem&gt;&amp;, const QString&amp;)">
        <inject-code class="target" position="beginning" file="../glue/qtdatavisualization.cpp"
                     snippet="dataproxy-insertrow-string"/>
    </add-function>

    <modify-function signature="setRow(int,QList&lt;QBarDataItem&gt;*)" remove="all"/>
    <add-function signature="setRow(int,const QList&lt;QBarDataItem&gt;&amp;)">
        <inject-code class="target" position="beginning" file="../glue/qtdatavisualization.cpp"
                     snippet="dataproxy-setrow"/>
    </add-function>
    <modify-function signature="setRow(int,QList&lt;QBarDataItem&gt;*,const QString&amp;)" remove="all"/>
    <add-function signature="setRow(int,const QList&lt;QBarDataItem&gt;&amp;,const QString&amp;)">
        <inject-code class="target" position="beginning" file="../glue/qtdatavisualization.cpp"
                     snippet="dataproxy-setrow-string"/>
    </add-function>

    <modify-function signature="addRows(const QBarDataArray&amp;)">
      <modify-argument index="1">
        <parent index="this" action="add"/>
      </modify-argument>
    </modify-function>
    <modify-function signature="addRows(const QBarDataArray&amp;, const QStringList&amp;)">
      <modify-argument index="1">
        <parent index="this" action="add"/>
      </modify-argument>
    </modify-function>
    <modify-function signature="insertRows(int, const QBarDataArray&amp;)">
      <modify-argument index="2">
        <parent index="this" action="add"/>
      </modify-argument>
    </modify-function>
    <modify-function signature="insertRows(int, const QBarDataArray&amp;, const QStringList&amp;)">
      <modify-argument index="2">
        <parent index="this" action="add"/>
      </modify-argument>
    </modify-function>
    <modify-function signature="setRows(int, const QBarDataArray&amp;)">
      <modify-argument index="2">
        <parent index="this" action="add"/>
      </modify-argument>
    </modify-function>
    <modify-function signature="setRows(int, const QBarDataArray&amp;, const QStringList&amp;)">
      <modify-argument index="2">
        <parent index="this" action="add"/>
      </modify-argument>
    </modify-function>
  </object-type>
  <object-type name="QCustom3DItem"/>
  <object-type name="QCustom3DLabel"/>
  <object-type name="QCustom3DVolume">
      <modify-function signature="setTextureData(QList&lt;uchar&gt;*)" remove="all"/>
      <add-function signature="setTextureData(const QList&lt;uchar&gt;&amp;)">
          <inject-code class="target" position="beginning" file="../glue/qtdatavisualization.cpp"
                       snippet="qcustom3dvolume-settexturedata"/>
      </add-function>
  </object-type>
  <object-type name="QHeightMapSurfaceDataProxy"/>
  <object-type name="QItemModelBarDataProxy">
    <enum-type name="MultiMatchBehavior"/>
  </object-type>
  <object-type name="QItemModelScatterDataProxy"/>
  <object-type name="QItemModelSurfaceDataProxy">
    <enum-type name="MultiMatchBehavior"/>
  </object-type>
  <object-type name="QScatter3DSeries">
    <modify-function signature="setDataProxy(QScatterDataProxy*)">
      <modify-argument index="1">
        <parent index="this" action="add"/>
      </modify-argument>
    </modify-function>
  </object-type>
  <value-type name="QScatterDataItem"/>
  <object-type name="QScatterDataProxy">
    <modify-function signature="resetArray(QList&lt;QScatterDataItem&gt;*)"
                     remove="all"/>
    <add-function signature="resetArray(QList&lt;QScatterDataItem&gt;*)">
      <inject-code class="target" position="beginning" file="../glue/qtdatavisualization.cpp"
                   snippet="scatterdataproxy-resetarray"/>
    </add-function>
    <modify-function signature="addItem(const QScatterDataItem&amp;)">
      <modify-argument index="1">
        <parent index="this" action="add"/>
      </modify-argument>
    </modify-function>
    <modify-function signature="addItems(const QList&lt;QScatterDataItem&gt;&amp;)">
      <modify-argument index="1">
        <parent index="this" action="add"/>
      </modify-argument>
    </modify-function>
    <modify-function signature="insertItem(int, const QScatterDataItem&amp;)">
      <modify-argument index="2">
        <parent index="this" action="add"/>
      </modify-argument>
    </modify-function>
    <modify-function signature="insertItems(int, const QList&lt;QScatterDataItem&gt;&amp;)">
      <modify-argument index="2">
        <parent index="this" action="add"/>
      </modify-argument>
    </modify-function>
    <modify-function signature="setItem(int, const QScatterDataItem&amp;)">
      <modify-argument index="2">
        <parent index="this" action="add"/>
      </modify-argument>
    </modify-function>
    <modify-function signature="setItems(int, const QList&lt;QScatterDataItem&gt;&amp;)">
      <modify-argument index="2">
        <parent index="this" action="add"/>
      </modify-argument>
    </modify-function>
  </object-type>
  <object-type name="QSurface3DSeries">
    <enum-type name="DrawFlag" flags="DrawFlags"/>
    <modify-function signature="QSurface3DSeries(QSurfaceDataProxy*,QObject*)">
      <modify-argument index="1">
        <parent index="this" action="add"/>
      </modify-argument>
    </modify-function>
    <modify-function signature="setDataProxy(QSurfaceDataProxy*)">
      <modify-argument index="1">
        <parent index="this" action="add"/>
      </modify-argument>
    </modify-function>
  </object-type>
  <value-type name="QSurfaceDataItem"/>
  <object-type name="QSurfaceDataProxy">
    <inject-code class="native" position="beginning">
      #include &lt;sbknumpycheck.h&gt;
      #include &lt;qtdatavisualization_helper.h&gt;
    </inject-code>
    <!-- PYSIDE-1438: Replace all add/set/insertRow() taking a 'QList*' by overloads
         taking 'const QList &' since an allocated list needs to be passed. -->
    <modify-function signature="addRow(QList&lt;QSurfaceDataItem&gt;*)" remove="all"/>
    <add-function signature="addRow(const QList&lt;QSurfaceDataItem&gt;&amp;)" return-type="int">
        <inject-code class="target" position="beginning" file="../glue/qtdatavisualization.cpp"
                     snippet="dataproxy-addrow"/>
    </add-function>

    <modify-function signature="insertRow(int,QList&lt;QSurfaceDataItem&gt;*)" remove="all"/>
    <add-function signature="insertRow(int,const QList&lt;QSurfaceDataItem&gt;&amp;)">
        <inject-code class="target" position="beginning" file="../glue/qtdatavisualization.cpp"
                     snippet="dataproxy-insertrow"/>
    </add-function>

    <modify-function signature="setRow(int,QList&lt;QSurfaceDataItem&gt;*)" remove="all"/>
    <add-function signature="setRow(int,const QList&lt;QSurfaceDataItem&gt;&amp;)">
        <inject-code class="target" position="beginning" file="../glue/qtdatavisualization.cpp"
                     snippet="dataproxy-setrow"/>
    </add-function>

    <modify-function signature="resetArray(QSurfaceDataArray*)" remove="all"/>
    <add-function signature="resetArray(const QSurfaceDataArray&amp;)">
      <inject-code class="target" position="beginning" file="../glue/qtdatavisualization.cpp"
                   snippet="dataproxy-resetarray"/>
      <modify-argument index="1">
        <parent index="this" action="add"/>
      </modify-argument>
    </add-function>

    <add-function signature="resetArrayNp(double@x@,double@deltaX@,double@z@,double@deltaZ@,PyArrayObject*@data@)">
        <inject-code file="../glue/qtdatavisualization.cpp"
                     snippet="qsurfacedataproxy-resetarraynp"/>
        <inject-documentation format="target" mode="append">
        Populates the data from a 2 dimensional numpy array containing the y
        values for a range starting a ``x``, ``z`` with steps of ``deltaX``,
        ``deltaZ``, respectively.
        </inject-documentation>
    </add-function>

  </object-type>
  <object-type name="Q3DBars">
    <modify-function signature="addAxis(QAbstract3DAxis*)">
      <modify-argument index="1">
        <parent index="this" action="add"/>
      </modify-argument>
    </modify-function>
    <modify-function signature="releaseAxis(QAbstract3DAxis*)">
      <modify-argument index="1">
        <reference-count action="set"/>
      </modify-argument>
      <inject-code file="../glue/qtdatavisualization.cpp" snippet="releaseownership"/>
    </modify-function>
    <modify-function signature="setColumnAxis(QCategory3DAxis*)">
      <modify-argument index="1">
        <parent index="this" action="add"/>
      </modify-argument>
    </modify-function>
    <modify-function signature="setRowAxis(QCategory3DAxis*)">
      <modify-argument index="1">
        <parent index="this" action="add"/>
      </modify-argument>
    </modify-function>
  </object-type>
  <object-type name="Q3DCamera">
    <enum-type name="CameraPreset"/>
  </object-type>
  <object-type name="Q3DLight"/>
  <object-type name="Q3DObject"/>
  <object-type name="Q3DScatter">
    <modify-function signature="addAxis(QValue3DAxis*)">
      <modify-argument index="1">
        <parent index="this" action="add"/>
      </modify-argument>
    </modify-function>
    <modify-function signature="releaseAxis(QValue3DAxis*)">
      <modify-argument index="1">
        <reference-count action="set"/>
      </modify-argument>
      <inject-code file="../glue/qtdatavisualization.cpp" snippet="releaseownership"/>
    </modify-function>
    <modify-function signature="setAxisX(QValue3DAxis*)">
      <modify-argument index="1">
        <parent index="this" action="add"/>
      </modify-argument>
    </modify-function>
    <modify-function signature="setAxisY(QValue3DAxis*)">
      <modify-argument index="1">
        <parent index="this" action="add"/>
      </modify-argument>
    </modify-function>
    <modify-function signature="setAxisZ(QValue3DAxis*)">
      <modify-argument index="1">
        <parent index="this" action="add"/>
      </modify-argument>
    </modify-function>
  </object-type>
  <object-type name="Q3DScene"/>
  <object-type name="Q3DSurface">
    <modify-function signature="addAxis(QValue3DAxis*)">
      <modify-argument index="1">
        <parent index="this" action="add"/>
      </modify-argument>
    </modify-function>
    <modify-function signature="releaseAxis(QValue3DAxis*)">
      <modify-argument index="1">
        <reference-count action="set"/>
      </modify-argument>
      <inject-code file="../glue/qtdatavisualization.cpp" snippet="releaseownership"/>
    </modify-function>
    <modify-function signature="setAxisX(QValue3DAxis*)">
      <modify-argument index="1">
        <parent index="this" action="add"/>
      </modify-argument>
    </modify-function>
    <modify-function signature="setAxisY(QValue3DAxis*)">
      <modify-argument index="1">
        <parent index="this" action="add"/>
      </modify-argument>
    </modify-function>
    <modify-function signature="setAxisZ(QValue3DAxis*)">
      <modify-argument index="1">
        <parent index="this" action="add"/>
      </modify-argument>
    </modify-function>
  </object-type>
  <object-type name="QAbstract3DGraph">
    <enum-type name="ElementType"/>
    <enum-type name="OptimizationHint" flags="OptimizationHints"/>
    <enum-type name="SelectionFlag" flags="SelectionFlags"/>
    <enum-type name="ShadowQuality"/>
    <modify-function signature="addCustomItem(QCustom3DItem*)">
      <modify-argument index="1">
        <parent index="this" action="add"/>
      </modify-argument>
    </modify-function>
    <modify-function signature="addInputHandler(QAbstract3DInputHandler*)">
      <modify-argument index="1">
        <parent index="this" action="add"/>
      </modify-argument>
    </modify-function>
    <modify-function signature="addTheme(Q3DTheme*)">
      <modify-argument index="1">
        <parent index="this" action="add"/>
      </modify-argument>
    </modify-function>
    <modify-function signature="releaseCustomItem(QCustom3DItem*)">
      <modify-argument index="1">
        <reference-count action="set"/>
      </modify-argument>
      <inject-code file="../glue/qtdatavisualization.cpp" snippet="releaseownership"/>
    </modify-function>
    <modify-function signature="releaseInputHandler(QAbstract3DInputHandler*)">
      <modify-argument index="1">
        <reference-count action="set"/>
      </modify-argument>
      <inject-code file="../glue/qtdatavisualization.cpp" snippet="releaseownership"/>
    </modify-function>
    <modify-function signature="releaseTheme(Q3DTheme*)">
      <modify-argument index="1">
        <reference-count action="set"/>
      </modify-argument>
      <inject-code file="../glue/qtdatavisualization.cpp" snippet="releaseownership"/>
    </modify-function>
    <modify-function signature="setActiveInputHandler(QAbstract3DInputHandler*)">
      <modify-argument index="1">
        <parent index="this" action="add"/>
      </modify-argument>
    </modify-function>
    <modify-function signature="setActiveTheme(Q3DTheme*)">
      <modify-argument index="1">
        <parent index="this" action="add"/>
      </modify-argument>
    </modify-function>
  </object-type>
  <object-type name="Q3DInputHandler"/>
  <object-type name="QAbstract3DInputHandler">
    <enum-type name="InputView"/>
  </object-type>
  <object-type name="QTouch3DInputHandler"/>
  <object-type name="Q3DTheme">
    <enum-type name="ColorStyle"/>
    <enum-type name="Theme"/>
  </object-type>
  <extra-includes>
    <include file-name="qutils.h" location="global"/>
  </extra-includes>

  <!-- QtQml/QtNetwork are pulled in via QtDataVisualizationDepends. -->
  <suppress-warning text="^Scoped enum 'Q(Ocsp)|(Dtls)|(Qml).*' does not have a type entry.*$"/>

</typesystem>
