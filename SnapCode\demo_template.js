// Demo template to showcase the new placeholder features
function {{FUNCTION_NAME}}({{PARAMETERS}}) {
    const {{VARIABLE_NAME}} = "{{DEFAULT_VALUE}}";
    
    if ({{CONDITION}}) {
        console.log("{{LOG_MESSAGE}}");
        return {{SUCCESS_RETURN}};
    }
    
    return {{ERROR_RETURN}};
}

class {{CLASS_NAME}} {
    constructor({{CONSTRUCTOR_PARAMS}}) {
        this.{{PROPERTY_NAME}} = {{PROPERTY_VALUE}};
    }
    
    {{METHOD_NAME}}() {
        // {{METHOD_DESCRIPTION}}
        return this.{{PROPERTY_NAME}};
    }
}

export default {{CLASS_NAME}};
