import QtQuick.tooling 1.2

// This file describes the plugin-supplied types contained in the library.
// It is used for QML tooling purposes only.
//
// This file was auto-generated by qmltyperegistrar.

Module {
    Component {
        file: "private/conegeometry_p.h"
        name: "ConeGeometry"
        accessSemantics: "reference"
        prototype: "QQuick3DGeometry"
        exports: ["QtQuick3D.Helpers/ConeGeometry 6.9"]
        exportMetaObjectRevisions: [1545]
        Enum {
            name: "Status"
            values: ["Null", "Ready", "Loading", "Error"]
        }
        Property {
            name: "topRadius"
            type: "float"
            read: "topRadius"
            write: "setTopRadius"
            notify: "topRadiusChanged"
            index: 0
            isFinal: true
        }
        Property {
            name: "bottomRadius"
            type: "float"
            read: "bottomRadius"
            write: "setBottomRadius"
            notify: "bottomRadiusChanged"
            index: 1
            isFinal: true
        }
        Property {
            name: "length"
            type: "float"
            read: "length"
            write: "setLength"
            notify: "lengthChanged"
            index: 2
            isFinal: true
        }
        Property {
            name: "rings"
            type: "int"
            read: "rings"
            write: "setRings"
            notify: "ringsChanged"
            index: 3
            isFinal: true
        }
        Property {
            name: "segments"
            type: "int"
            read: "segments"
            write: "setSegments"
            notify: "segmentsChanged"
            index: 4
            isFinal: true
        }
        Property {
            name: "asynchronous"
            type: "bool"
            read: "asynchronous"
            write: "setAsynchronous"
            notify: "asynchronousChanged"
            index: 5
        }
        Property {
            name: "status"
            type: "Status"
            read: "status"
            notify: "statusChanged"
            index: 6
            isReadonly: true
        }
        Signal { name: "topRadiusChanged" }
        Signal { name: "bottomRadiusChanged" }
        Signal { name: "lengthChanged" }
        Signal { name: "ringsChanged" }
        Signal { name: "segmentsChanged" }
        Signal { name: "asynchronousChanged" }
        Signal { name: "statusChanged" }
        Method { name: "doUpdateGeometry" }
        Method { name: "requestFinished" }
    }
    Component {
        file: "private/cuboidgeometry_p.h"
        name: "CuboidGeometry"
        accessSemantics: "reference"
        prototype: "QQuick3DGeometry"
        exports: ["QtQuick3D.Helpers/CuboidGeometry 6.9"]
        exportMetaObjectRevisions: [1545]
        Enum {
            name: "Status"
            values: ["Null", "Ready", "Loading", "Error"]
        }
        Property {
            name: "xExtent"
            type: "float"
            read: "xExtent"
            write: "setXExtent"
            notify: "xExtentChanged"
            index: 0
            isFinal: true
        }
        Property {
            name: "yExtent"
            type: "float"
            read: "yExtent"
            write: "setYExtent"
            notify: "yExtentChanged"
            index: 1
            isFinal: true
        }
        Property {
            name: "zExtent"
            type: "float"
            read: "zExtent"
            write: "setZExtent"
            notify: "zExtentChanged"
            index: 2
            isFinal: true
        }
        Property {
            name: "yzMeshResolution"
            type: "QSize"
            read: "yzMeshResolution"
            write: "setYzMeshResolution"
            notify: "yzMeshResolutionChanged"
            index: 3
            isFinal: true
        }
        Property {
            name: "xzMeshResolution"
            type: "QSize"
            read: "xzMeshResolution"
            write: "setXzMeshResolution"
            notify: "xzMeshResolutionChanged"
            index: 4
            isFinal: true
        }
        Property {
            name: "xyMeshResolution"
            type: "QSize"
            read: "xyMeshResolution"
            write: "setXyMeshResolution"
            notify: "xyMeshResolutionChanged"
            index: 5
            isFinal: true
        }
        Property {
            name: "asynchronous"
            type: "bool"
            read: "asynchronous"
            write: "setAsynchronous"
            notify: "asynchronousChanged"
            index: 6
        }
        Property {
            name: "status"
            type: "Status"
            read: "status"
            notify: "statusChanged"
            index: 7
            isReadonly: true
        }
        Signal { name: "xExtentChanged" }
        Signal { name: "yExtentChanged" }
        Signal { name: "zExtentChanged" }
        Signal { name: "yzMeshResolutionChanged" }
        Signal { name: "xzMeshResolutionChanged" }
        Signal { name: "xyMeshResolutionChanged" }
        Signal { name: "asynchronousChanged" }
        Signal { name: "statusChanged" }
        Method { name: "doUpdateGeometry" }
        Method { name: "requestFinished" }
    }
    Component {
        file: "private/cylindergeometry_p.h"
        name: "CylinderGeometry"
        accessSemantics: "reference"
        prototype: "QQuick3DGeometry"
        exports: ["QtQuick3D.Helpers/CylinderGeometry 6.9"]
        exportMetaObjectRevisions: [1545]
        Enum {
            name: "Status"
            values: ["Null", "Ready", "Loading", "Error"]
        }
        Property {
            name: "radius"
            type: "float"
            read: "radius"
            write: "setRadius"
            notify: "radiusChanged"
            index: 0
            isFinal: true
        }
        Property {
            name: "length"
            type: "float"
            read: "length"
            write: "setLength"
            notify: "lengthChanged"
            index: 1
            isFinal: true
        }
        Property {
            name: "rings"
            type: "int"
            read: "rings"
            write: "setRings"
            notify: "ringsChanged"
            index: 2
            isFinal: true
        }
        Property {
            name: "segments"
            type: "int"
            read: "segments"
            write: "setSegments"
            notify: "segmentsChanged"
            index: 3
            isFinal: true
        }
        Property {
            name: "asynchronous"
            type: "bool"
            read: "asynchronous"
            write: "setAsynchronous"
            notify: "asynchronousChanged"
            index: 4
        }
        Property {
            name: "status"
            type: "Status"
            read: "status"
            notify: "statusChanged"
            index: 5
            isReadonly: true
        }
        Signal { name: "radiusChanged" }
        Signal { name: "lengthChanged" }
        Signal { name: "ringsChanged" }
        Signal { name: "segmentsChanged" }
        Signal { name: "asynchronousChanged" }
        Signal { name: "statusChanged" }
        Method { name: "doUpdateGeometry" }
        Method { name: "requestFinished" }
    }
    Component {
        file: "private/extrudedtextgeometry_p.h"
        name: "ExtrudedTextGeometry"
        accessSemantics: "reference"
        prototype: "QQuick3DGeometry"
        exports: ["QtQuick3D.Helpers/ExtrudedTextGeometry 6.9"]
        exportMetaObjectRevisions: [1545]
        Enum {
            name: "Status"
            values: ["Null", "Ready", "Loading", "Error"]
        }
        Property {
            name: "text"
            type: "QString"
            read: "text"
            write: "setText"
            notify: "textChanged"
            index: 0
        }
        Property {
            name: "font"
            type: "QFont"
            read: "font"
            write: "setFont"
            notify: "fontChanged"
            index: 1
        }
        Property {
            name: "depth"
            type: "float"
            read: "depth"
            write: "setDepth"
            notify: "depthChanged"
            index: 2
        }
        Property {
            name: "scale"
            type: "float"
            read: "scale"
            write: "setScale"
            notify: "scaleChanged"
            index: 3
            isFinal: true
        }
        Property {
            name: "asynchronous"
            type: "bool"
            read: "asynchronous"
            write: "setAsynchronous"
            notify: "asynchronousChanged"
            index: 4
        }
        Property {
            name: "status"
            type: "Status"
            read: "status"
            notify: "statusChanged"
            index: 5
            isReadonly: true
        }
        Signal { name: "textChanged" }
        Signal { name: "fontChanged" }
        Signal { name: "depthChanged" }
        Signal { name: "scaleChanged" }
        Signal { name: "asynchronousChanged" }
        Signal { name: "statusChanged" }
        Method { name: "doUpdateGeometry" }
        Method { name: "requestFinished" }
    }
    Component {
        file: "private/gridgeometry_p.h"
        name: "GridGeometry"
        accessSemantics: "reference"
        prototype: "QQuick3DGeometry"
        exports: [
            "QtQuick3D.Helpers/GridGeometry 6.0",
            "QtQuick3D.Helpers/GridGeometry 6.7"
        ]
        exportMetaObjectRevisions: [1536, 1543]
        Property {
            name: "horizontalLines"
            type: "int"
            read: "horizontalLines"
            write: "setHorizontalLines"
            notify: "horizontalLinesChanged"
            index: 0
        }
        Property {
            name: "verticalLines"
            type: "int"
            read: "verticalLines"
            write: "setVerticalLines"
            notify: "verticalLinesChanged"
            index: 1
        }
        Property {
            name: "horizontalStep"
            type: "float"
            read: "horizontalStep"
            write: "setHorizontalStep"
            notify: "horizontalStepChanged"
            index: 2
        }
        Property {
            name: "verticalStep"
            type: "float"
            read: "verticalStep"
            write: "setVerticalStep"
            notify: "verticalStepChanged"
            index: 3
        }
        Signal { name: "horizontalLinesChanged" }
        Signal { name: "verticalLinesChanged" }
        Signal { name: "horizontalStepChanged" }
        Signal { name: "verticalStepChanged" }
        Method {
            name: "setHorizontalLines"
            Parameter { name: "count"; type: "int" }
        }
        Method {
            name: "setVerticalLines"
            Parameter { name: "count"; type: "int" }
        }
        Method {
            name: "setHorizontalStep"
            Parameter { name: "step"; type: "float" }
        }
        Method {
            name: "setVerticalStep"
            Parameter { name: "step"; type: "float" }
        }
    }
    Component {
        file: "private/heightfieldgeometry_p.h"
        name: "HeightFieldGeometry"
        accessSemantics: "reference"
        prototype: "QQuick3DGeometry"
        exports: [
            "QtQuick3D.Helpers/HeightFieldGeometry 6.0",
            "QtQuick3D.Helpers/HeightFieldGeometry 6.5",
            "QtQuick3D.Helpers/HeightFieldGeometry 6.7"
        ]
        exportMetaObjectRevisions: [1536, 1541, 1543]
        Property {
            name: "source"
            revision: 1541
            type: "QUrl"
            read: "source"
            write: "setSource"
            notify: "sourceChanged"
            index: 0
        }
        Property {
            name: "smoothShading"
            type: "bool"
            read: "smoothShading"
            write: "setSmoothShading"
            notify: "smoothShadingChanged"
            index: 1
        }
        Property {
            name: "extents"
            type: "QVector3D"
            read: "extents"
            write: "setExtents"
            notify: "extentsChanged"
            index: 2
        }
        Property {
            name: "heightMap"
            type: "QUrl"
            read: "source"
            write: "setSource"
            notify: "sourceChanged"
            index: 3
        }
        Signal { name: "sourceChanged" }
        Signal { name: "smoothShadingChanged" }
        Signal { name: "extentsChanged" }
    }
    Component {
        file: "private/instancerepeater_p.h"
        name: "InstanceModel"
        accessSemantics: "reference"
        prototype: "QAbstractListModel"
        exports: ["QtQuick3D.Helpers/InstanceModel 6.4"]
        exportMetaObjectRevisions: [1540]
        Property {
            name: "instancingTable"
            type: "QQuick3DInstancing"
            isPointer: true
            read: "instancing"
            write: "setInstancing"
            notify: "instancingChanged"
            index: 0
        }
        Signal { name: "instancingChanged" }
        Method { name: "reset" }
    }
    Component {
        file: "private/instancerepeater_p.h"
        name: "InstanceRepeater"
        accessSemantics: "reference"
        defaultProperty: "delegate"
        prototype: "QQuick3DRepeater"
        exports: ["QtQuick3D.Helpers/InstanceRepeater 6.4"]
        exportMetaObjectRevisions: [1540]
        Property {
            name: "instancingTable"
            type: "QQuick3DInstancing"
            isPointer: true
            read: "instancing"
            write: "setInstancing"
            notify: "instancingChanged"
            index: 0
        }
        Signal { name: "instancingChanged" }
    }
    Component {
        file: "private/lookatnode_p.h"
        name: "LookAtNode"
        accessSemantics: "reference"
        prototype: "QQuick3DNode"
        exports: ["QtQuick3D.Helpers/LookAtNode 6.4"]
        exportMetaObjectRevisions: [1540]
        Property {
            name: "target"
            type: "QQuick3DNode"
            isPointer: true
            read: "target"
            write: "setTarget"
            notify: "targetChanged"
            index: 0
        }
        Signal { name: "targetChanged" }
        Method {
            name: "setTarget"
            Parameter { name: "node"; type: "QQuick3DNode"; isPointer: true }
        }
        Method { name: "updateLookAt" }
    }
    Component {
        file: "private/planegeometry_p.h"
        name: "PlaneGeometry"
        accessSemantics: "reference"
        prototype: "QQuick3DGeometry"
        exports: ["QtQuick3D.Helpers/PlaneGeometry 6.9"]
        exportMetaObjectRevisions: [1545]
        Enum {
            name: "Status"
            values: ["Null", "Ready", "Loading", "Error"]
        }
        Enum {
            name: "Plane"
            values: ["XY", "XZ", "ZY"]
        }
        Property {
            name: "width"
            type: "float"
            read: "width"
            write: "setWidth"
            notify: "widthChanged"
            index: 0
            isFinal: true
        }
        Property {
            name: "height"
            type: "float"
            read: "height"
            write: "setHeight"
            notify: "heightChanged"
            index: 1
            isFinal: true
        }
        Property {
            name: "meshResolution"
            type: "QSize"
            read: "meshResolution"
            write: "setMeshResolution"
            notify: "meshResolutionChanged"
            index: 2
            isFinal: true
        }
        Property {
            name: "plane"
            type: "Plane"
            read: "plane"
            write: "setPlane"
            notify: "planeChanged"
            index: 3
            isFinal: true
        }
        Property {
            name: "reversed"
            type: "bool"
            read: "reversed"
            write: "setReversed"
            notify: "reversedChanged"
            index: 4
            isFinal: true
        }
        Property {
            name: "mirrored"
            type: "bool"
            read: "mirrored"
            write: "setMirrored"
            notify: "mirroredChanged"
            index: 5
            isFinal: true
        }
        Property {
            name: "asynchronous"
            type: "bool"
            read: "asynchronous"
            write: "setAsynchronous"
            notify: "asynchronousChanged"
            index: 6
        }
        Property {
            name: "status"
            type: "Status"
            read: "status"
            notify: "statusChanged"
            index: 7
            isReadonly: true
        }
        Signal { name: "widthChanged" }
        Signal { name: "heightChanged" }
        Signal { name: "meshResolutionChanged" }
        Signal { name: "planeChanged" }
        Signal { name: "mirroredChanged" }
        Signal { name: "asynchronousChanged" }
        Signal { name: "statusChanged" }
        Signal { name: "reversedChanged" }
        Method { name: "doUpdateGeometry" }
        Method { name: "requestFinished" }
    }
    Component {
        file: "private/proceduralmesh_p.h"
        name: "ProceduralMesh"
        accessSemantics: "reference"
        prototype: "QQuick3DGeometry"
        exports: [
            "QtQuick3D.Helpers/ProceduralMesh 6.6",
            "QtQuick3D.Helpers/ProceduralMesh 6.7"
        ]
        exportMetaObjectRevisions: [1542, 1543]
        Enum {
            name: "PrimitiveMode"
            values: [
                "Points",
                "LineStrip",
                "Lines",
                "TriangleStrip",
                "TriangleFan",
                "Triangles"
            ]
        }
        Property {
            name: "positions"
            type: "QVector3D"
            isList: true
            read: "positions"
            write: "setPositions"
            notify: "positionsChanged"
            index: 0
            isFinal: true
        }
        Property {
            name: "normals"
            type: "QVector3D"
            isList: true
            read: "normals"
            write: "setNormals"
            notify: "normalsChanged"
            index: 1
            isFinal: true
        }
        Property {
            name: "tangents"
            type: "QVector3D"
            isList: true
            read: "tangents"
            write: "setTangents"
            notify: "tangentsChanged"
            index: 2
            isFinal: true
        }
        Property {
            name: "binormals"
            type: "QVector3D"
            isList: true
            read: "binormals"
            write: "setBinormals"
            notify: "binormalsChanged"
            index: 3
            isFinal: true
        }
        Property {
            name: "uv0s"
            type: "QVector2D"
            isList: true
            read: "uv0s"
            write: "setUv0s"
            notify: "uv0sChanged"
            index: 4
            isFinal: true
        }
        Property {
            name: "uv1s"
            type: "QVector2D"
            isList: true
            read: "uv1s"
            write: "setUv1s"
            notify: "uv1sChanged"
            index: 5
            isFinal: true
        }
        Property {
            name: "colors"
            type: "QVector4D"
            isList: true
            read: "colors"
            write: "setColors"
            notify: "colorsChanged"
            index: 6
            isFinal: true
        }
        Property {
            name: "joints"
            type: "QVector4D"
            isList: true
            read: "joints"
            write: "setJoints"
            notify: "jointsChanged"
            index: 7
            isFinal: true
        }
        Property {
            name: "weights"
            type: "QVector4D"
            isList: true
            read: "weights"
            write: "setWeights"
            notify: "weightsChanged"
            index: 8
            isFinal: true
        }
        Property {
            name: "indexes"
            type: "uint"
            isList: true
            read: "indexes"
            write: "setIndexes"
            notify: "indexesChanged"
            index: 9
            isFinal: true
        }
        Property {
            name: "subsets"
            type: "ProceduralMeshSubset"
            isList: true
            read: "subsets"
            index: 10
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "primitiveMode"
            type: "PrimitiveMode"
            read: "primitiveMode"
            write: "setPrimitiveMode"
            notify: "primitiveModeChanged"
            index: 11
            isFinal: true
        }
        Signal { name: "positionsChanged" }
        Signal { name: "primitiveModeChanged" }
        Signal { name: "indexesChanged" }
        Signal { name: "normalsChanged" }
        Signal { name: "tangentsChanged" }
        Signal { name: "binormalsChanged" }
        Signal { name: "uv0sChanged" }
        Signal { name: "uv1sChanged" }
        Signal { name: "colorsChanged" }
        Signal { name: "jointsChanged" }
        Signal { name: "weightsChanged" }
        Method { name: "requestUpdate" }
        Method { name: "updateGeometry" }
        Method {
            name: "subsetDestroyed"
            Parameter { name: "subset"; type: "QObject"; isPointer: true }
        }
    }
    Component {
        file: "private/proceduralmesh_p.h"
        name: "ProceduralMeshSubset"
        accessSemantics: "reference"
        prototype: "QObject"
        exports: ["QtQuick3D.Helpers/ProceduralMeshSubset 6.6"]
        exportMetaObjectRevisions: [1542]
        Property {
            name: "offset"
            type: "uint"
            read: "offset"
            write: "setOffset"
            notify: "offsetChanged"
            index: 0
            isFinal: true
        }
        Property {
            name: "count"
            type: "uint"
            read: "count"
            write: "setCount"
            notify: "countChanged"
            index: 1
            isFinal: true
        }
        Property {
            name: "name"
            type: "QString"
            read: "name"
            write: "setName"
            notify: "nameChanged"
            index: 2
            isFinal: true
        }
        Signal { name: "offsetChanged" }
        Signal { name: "countChanged" }
        Signal { name: "nameChanged" }
        Signal { name: "isDirty" }
    }
    Component {
        file: "private/proceduralskytexturedata_p.h"
        name: "ProceduralSkyTextureData"
        accessSemantics: "reference"
        prototype: "QQuick3DTextureData"
        exports: ["QtQuick3D.Helpers/ProceduralSkyTextureData 6.0"]
        exportMetaObjectRevisions: [1536]
        Enum {
            name: "SkyTextureQuality"
            isScoped: true
            values: [
                "SkyTextureQualityLow",
                "SkyTextureQualityMedium",
                "SkyTextureQualityHigh",
                "SkyTextureQualityVeryHigh"
            ]
        }
        Property {
            name: "skyTopColor"
            type: "QColor"
            read: "skyTopColor"
            write: "setSkyTopColor"
            notify: "skyTopColorChanged"
            index: 0
        }
        Property {
            name: "skyHorizonColor"
            type: "QColor"
            read: "skyHorizonColor"
            write: "setSkyHorizonColor"
            notify: "skyHorizonColorChanged"
            index: 1
        }
        Property {
            name: "skyCurve"
            type: "float"
            read: "skyCurve"
            write: "setSkyCurve"
            notify: "skyCurveChanged"
            index: 2
        }
        Property {
            name: "skyEnergy"
            type: "float"
            read: "skyEnergy"
            write: "setSkyEnergy"
            notify: "skyEnergyChanged"
            index: 3
        }
        Property {
            name: "groundBottomColor"
            type: "QColor"
            read: "groundBottomColor"
            write: "setGroundBottomColor"
            notify: "groundBottomColorChanged"
            index: 4
        }
        Property {
            name: "groundHorizonColor"
            type: "QColor"
            read: "groundHorizonColor"
            write: "setGroundHorizonColor"
            notify: "groundHorizonColorChanged"
            index: 5
        }
        Property {
            name: "groundCurve"
            type: "float"
            read: "groundCurve"
            write: "setGroundCurve"
            notify: "groundCurveChanged"
            index: 6
        }
        Property {
            name: "groundEnergy"
            type: "float"
            read: "groundEnergy"
            write: "setGroundEnergy"
            notify: "groundEnergyChanged"
            index: 7
        }
        Property {
            name: "sunColor"
            type: "QColor"
            read: "sunColor"
            write: "setSunColor"
            notify: "sunColorChanged"
            index: 8
        }
        Property {
            name: "sunLatitude"
            type: "float"
            read: "sunLatitude"
            write: "setSunLatitude"
            notify: "sunLatitudeChanged"
            index: 9
        }
        Property {
            name: "sunLongitude"
            type: "float"
            read: "sunLongitude"
            write: "setSunLongitude"
            notify: "sunLongitudeChanged"
            index: 10
        }
        Property {
            name: "sunAngleMin"
            type: "float"
            read: "sunAngleMin"
            write: "setSunAngleMin"
            notify: "sunAngleMinChanged"
            index: 11
        }
        Property {
            name: "sunAngleMax"
            type: "float"
            read: "sunAngleMax"
            write: "setSunAngleMax"
            notify: "sunAngleMaxChanged"
            index: 12
        }
        Property {
            name: "sunCurve"
            type: "float"
            read: "sunCurve"
            write: "setSunCurve"
            notify: "sunCurveChanged"
            index: 13
        }
        Property {
            name: "sunEnergy"
            type: "float"
            read: "sunEnergy"
            write: "setSunEnergy"
            notify: "sunEnergyChanged"
            index: 14
        }
        Property {
            name: "textureQuality"
            type: "SkyTextureQuality"
            read: "textureQuality"
            write: "setTextureQuality"
            notify: "textureQualityChanged"
            index: 15
        }
        Signal {
            name: "skyTopColorChanged"
            Parameter { name: "skyTopColor"; type: "QColor" }
        }
        Signal {
            name: "skyHorizonColorChanged"
            Parameter { name: "skyHorizonColor"; type: "QColor" }
        }
        Signal {
            name: "skyCurveChanged"
            Parameter { name: "skyCurve"; type: "float" }
        }
        Signal {
            name: "skyEnergyChanged"
            Parameter { name: "skyEnergy"; type: "float" }
        }
        Signal {
            name: "groundBottomColorChanged"
            Parameter { name: "groundBottomColor"; type: "QColor" }
        }
        Signal {
            name: "groundHorizonColorChanged"
            Parameter { name: "groundHorizonColor"; type: "QColor" }
        }
        Signal {
            name: "groundCurveChanged"
            Parameter { name: "groundCurve"; type: "float" }
        }
        Signal {
            name: "groundEnergyChanged"
            Parameter { name: "groundEnergy"; type: "float" }
        }
        Signal {
            name: "sunColorChanged"
            Parameter { name: "sunColor"; type: "QColor" }
        }
        Signal {
            name: "sunLatitudeChanged"
            Parameter { name: "sunLatitude"; type: "float" }
        }
        Signal {
            name: "sunLongitudeChanged"
            Parameter { name: "sunLongitude"; type: "float" }
        }
        Signal {
            name: "sunAngleMinChanged"
            Parameter { name: "sunAngleMin"; type: "float" }
        }
        Signal {
            name: "sunAngleMaxChanged"
            Parameter { name: "sunAngleMax"; type: "float" }
        }
        Signal {
            name: "sunCurveChanged"
            Parameter { name: "sunCurve"; type: "float" }
        }
        Signal {
            name: "sunEnergyChanged"
            Parameter { name: "sunEnergy"; type: "float" }
        }
        Signal {
            name: "textureQualityChanged"
            Parameter { name: "textureQuality"; type: "SkyTextureQuality" }
        }
        Method {
            name: "setSkyTopColor"
            Parameter { name: "skyTopColor"; type: "QColor" }
        }
        Method {
            name: "setSkyHorizonColor"
            Parameter { name: "skyHorizonColor"; type: "QColor" }
        }
        Method {
            name: "setSkyCurve"
            Parameter { name: "skyCurve"; type: "float" }
        }
        Method {
            name: "setSkyEnergy"
            Parameter { name: "skyEnergy"; type: "float" }
        }
        Method {
            name: "setGroundBottomColor"
            Parameter { name: "groundBottomColor"; type: "QColor" }
        }
        Method {
            name: "setGroundHorizonColor"
            Parameter { name: "groundHorizonColor"; type: "QColor" }
        }
        Method {
            name: "setGroundCurve"
            Parameter { name: "groundCurve"; type: "float" }
        }
        Method {
            name: "setGroundEnergy"
            Parameter { name: "groundEnergy"; type: "float" }
        }
        Method {
            name: "setSunColor"
            Parameter { name: "sunColor"; type: "QColor" }
        }
        Method {
            name: "setSunLatitude"
            Parameter { name: "sunLatitude"; type: "float" }
        }
        Method {
            name: "setSunLongitude"
            Parameter { name: "sunLongitude"; type: "float" }
        }
        Method {
            name: "setSunAngleMin"
            Parameter { name: "sunAngleMin"; type: "float" }
        }
        Method {
            name: "setSunAngleMax"
            Parameter { name: "sunAngleMax"; type: "float" }
        }
        Method {
            name: "setSunCurve"
            Parameter { name: "sunCurve"; type: "float" }
        }
        Method {
            name: "setSunEnergy"
            Parameter { name: "sunEnergy"; type: "float" }
        }
        Method {
            name: "setTextureQuality"
            Parameter { name: "textureQuality"; type: "SkyTextureQuality" }
        }
        Method { name: "generateRGBA16FTexture" }
    }
    Component {
        file: "private/infinitegrid_p.h"
        name: "QQuick3DInfiniteGrid"
        accessSemantics: "reference"
        prototype: "QObject"
        interfaces: ["QQmlParserStatus"]
        exports: ["QtQuick3D.Helpers/InfiniteGrid 6.0"]
        exportMetaObjectRevisions: [1536]
        Property {
            name: "visible"
            type: "bool"
            read: "visible"
            write: "setVisible"
            notify: "visibleChanged"
            index: 0
        }
        Property {
            name: "gridInterval"
            type: "float"
            read: "gridInterval"
            write: "setGridInterval"
            notify: "gridIntervalChanged"
            index: 1
        }
        Property {
            name: "gridAxes"
            type: "bool"
            read: "gridAxes"
            write: "setGridAxes"
            notify: "gridAxesChanged"
            index: 2
        }
        Signal { name: "visibleChanged" }
        Signal { name: "gridIntervalChanged" }
        Signal { name: "gridAxesChanged" }
    }
    Component {
        file: "private/randominstancing_p.h"
        name: "QQuick3DInstanceRange"
        accessSemantics: "reference"
        defaultProperty: "data"
        prototype: "QQuick3DObject"
        exports: ["QtQuick3D.Helpers/InstanceRange 6.2"]
        exportMetaObjectRevisions: [1538]
        Property {
            name: "from"
            type: "QVariant"
            read: "from"
            write: "setFrom"
            notify: "fromChanged"
            index: 0
        }
        Property { name: "to"; type: "QVariant"; read: "to"; write: "setTo"; notify: "toChanged"; index: 1 }
        Property {
            name: "proportional"
            type: "bool"
            read: "proportional"
            write: "setProportional"
            notify: "proportionalChanged"
            index: 2
        }
        Signal { name: "fromChanged" }
        Signal { name: "toChanged" }
        Signal { name: "proportionalChanged" }
        Signal { name: "changed" }
        Method {
            name: "setFrom"
            Parameter { name: "from"; type: "QVariant" }
        }
        Method {
            name: "setTo"
            Parameter { name: "to"; type: "QVariant" }
        }
        Method {
            name: "setProportional"
            Parameter { name: "proportional"; type: "bool" }
        }
    }
    Component {
        file: "private/randominstancing_p.h"
        name: "QQuick3DRandomInstancing"
        accessSemantics: "reference"
        prototype: "QQuick3DInstancing"
        exports: [
            "QtQuick3D.Helpers/RandomInstancing 6.2",
            "QtQuick3D.Helpers/RandomInstancing 6.3",
            "QtQuick3D.Helpers/RandomInstancing 6.9"
        ]
        exportMetaObjectRevisions: [1538, 1539, 1545]
        Enum {
            name: "ColorModel"
            isScoped: true
            values: ["RGB", "HSV", "HSL"]
        }
        Property {
            name: "instanceCount"
            type: "int"
            read: "instanceCount"
            write: "setInstanceCount"
            notify: "instanceCountChanged"
            index: 0
        }
        Property {
            name: "position"
            type: "QQuick3DInstanceRange"
            isPointer: true
            read: "position"
            write: "setPosition"
            notify: "positionChanged"
            index: 1
        }
        Property {
            name: "scale"
            type: "QQuick3DInstanceRange"
            isPointer: true
            read: "scale"
            write: "setScale"
            notify: "scaleChanged"
            index: 2
        }
        Property {
            name: "rotation"
            type: "QQuick3DInstanceRange"
            isPointer: true
            read: "rotation"
            write: "setRotation"
            notify: "rotationChanged"
            index: 3
        }
        Property {
            name: "color"
            type: "QQuick3DInstanceRange"
            isPointer: true
            read: "color"
            write: "setColor"
            notify: "colorChanged"
            index: 4
        }
        Property {
            name: "colorModel"
            type: "ColorModel"
            read: "colorModel"
            write: "setColorModel"
            notify: "colorModelChanged"
            index: 5
        }
        Property {
            name: "customData"
            type: "QQuick3DInstanceRange"
            isPointer: true
            read: "customData"
            write: "setCustomData"
            notify: "customDataChanged"
            index: 6
        }
        Property {
            name: "gridSpacing"
            revision: 1545
            type: "QVector3D"
            read: "gridSpacing"
            write: "setGridSpacing"
            notify: "gridSpacingChanged"
            index: 7
            isFinal: true
        }
        Property {
            name: "randomSeed"
            type: "int"
            read: "randomSeed"
            write: "setRandomSeed"
            notify: "randomSeedChanged"
            index: 8
        }
        Signal { name: "instanceCountChanged" }
        Signal { name: "randomSeedChanged" }
        Signal { name: "positionChanged" }
        Signal { name: "scaleChanged" }
        Signal { name: "rotationChanged" }
        Signal { name: "colorChanged" }
        Signal { name: "customDataChanged" }
        Signal { name: "colorModelChanged" }
        Signal { name: "gridSpacingChanged" }
        Method {
            name: "setInstanceCount"
            Parameter { name: "instanceCount"; type: "int" }
        }
        Method {
            name: "setRandomSeed"
            Parameter { name: "randomSeed"; type: "int" }
        }
        Method {
            name: "setPosition"
            Parameter { name: "position"; type: "QQuick3DInstanceRange"; isPointer: true }
        }
        Method {
            name: "setScale"
            Parameter { name: "scale"; type: "QQuick3DInstanceRange"; isPointer: true }
        }
        Method {
            name: "setRotation"
            Parameter { name: "rotation"; type: "QQuick3DInstanceRange"; isPointer: true }
        }
        Method {
            name: "setColor"
            Parameter { name: "color"; type: "QQuick3DInstanceRange"; isPointer: true }
        }
        Method {
            name: "setCustomData"
            Parameter { name: "customData"; type: "QQuick3DInstanceRange"; isPointer: true }
        }
        Method {
            name: "setColorModel"
            Parameter { name: "colorModel"; type: "ColorModel" }
        }
        Method { name: "handleChange" }
    }
    Component {
        file: "private/qquick3dtexturedatafrontend_p.h"
        name: "QQuick3DTextureDataFrontend"
        accessSemantics: "reference"
        prototype: "QQuick3DTextureData"
        exports: ["QtQuick3D.Helpers/ProceduralTextureData 6.6"]
        exportMetaObjectRevisions: [1542]
        Property {
            name: "format"
            type: "QQuick3DTextureData::Format"
            read: "format"
            write: "setFormat"
            notify: "formatChanged"
            index: 0
        }
        Property {
            name: "width"
            type: "int"
            read: "width"
            write: "setWidth"
            notify: "widthChanged"
            index: 1
        }
        Property {
            name: "height"
            type: "int"
            read: "height"
            write: "setHeight"
            notify: "heightChanged"
            index: 2
        }
        Property {
            name: "depth"
            type: "int"
            read: "depth"
            write: "setDepth"
            notify: "depthChanged"
            index: 3
        }
        Property {
            name: "hasTransparency"
            type: "bool"
            read: "hasTransparency"
            write: "setHasTransparency"
            notify: "hasTransparencyChanged"
            index: 4
        }
        Property {
            name: "textureData"
            type: "QByteArray"
            read: "textureData"
            write: "setTextureData"
            notify: "textureDataChanged"
            index: 5
        }
        Signal { name: "formatChanged" }
        Signal { name: "depthChanged" }
        Signal { name: "hasTransparencyChanged" }
        Signal { name: "textureDataChanged" }
        Signal { name: "widthChanged" }
        Signal { name: "heightChanged" }
    }
    Component {
        file: "private/spheregeometry_p.h"
        name: "SphereGeometry"
        accessSemantics: "reference"
        prototype: "QQuick3DGeometry"
        exports: ["QtQuick3D.Helpers/SphereGeometry 6.9"]
        exportMetaObjectRevisions: [1545]
        Enum {
            name: "Status"
            values: ["Null", "Ready", "Loading", "Error"]
        }
        Property {
            name: "radius"
            type: "float"
            read: "radius"
            write: "setRadius"
            notify: "radiusChanged"
            index: 0
            isFinal: true
        }
        Property {
            name: "rings"
            type: "int"
            read: "rings"
            write: "setRings"
            notify: "ringsChanged"
            index: 1
            isFinal: true
        }
        Property {
            name: "segments"
            type: "int"
            read: "segments"
            write: "setSegments"
            notify: "segmentsChanged"
            index: 2
            isFinal: true
        }
        Property {
            name: "asynchronous"
            type: "bool"
            read: "asynchronous"
            write: "setAsynchronous"
            notify: "asynchronousChanged"
            index: 3
        }
        Property {
            name: "status"
            type: "Status"
            read: "status"
            notify: "statusChanged"
            index: 4
            isReadonly: true
        }
        Signal { name: "radiusChanged" }
        Signal { name: "ringsChanged" }
        Signal { name: "segmentsChanged" }
        Signal { name: "asynchronousChanged" }
        Signal { name: "statusChanged" }
        Method { name: "doUpdateGeometry" }
        Method { name: "requestFinished" }
    }
    Component {
        file: "private/torusgeometry_p.h"
        name: "TorusGeometry"
        accessSemantics: "reference"
        prototype: "QQuick3DGeometry"
        exports: ["QtQuick3D.Helpers/TorusGeometry 6.9"]
        exportMetaObjectRevisions: [1545]
        Enum {
            name: "Status"
            values: ["Null", "Ready", "Loading", "Error"]
        }
        Property {
            name: "rings"
            type: "int"
            read: "rings"
            write: "setRings"
            notify: "ringsChanged"
            index: 0
            isFinal: true
        }
        Property {
            name: "segments"
            type: "int"
            read: "segments"
            write: "setSegments"
            notify: "segmentsChanged"
            index: 1
            isFinal: true
        }
        Property {
            name: "radius"
            type: "float"
            read: "radius"
            write: "setRadius"
            notify: "radiusChanged"
            index: 2
            isFinal: true
        }
        Property {
            name: "tubeRadius"
            type: "float"
            read: "tubeRadius"
            write: "setTubeRadius"
            notify: "tubeRadiusChanged"
            index: 3
            isFinal: true
        }
        Property {
            name: "asynchronous"
            type: "bool"
            read: "asynchronous"
            write: "setAsynchronous"
            notify: "asynchronousChanged"
            index: 4
        }
        Property {
            name: "status"
            type: "Status"
            read: "status"
            notify: "statusChanged"
            index: 5
            isReadonly: true
        }
        Signal { name: "ringsChanged" }
        Signal { name: "segmentsChanged" }
        Signal { name: "radiusChanged" }
        Signal { name: "tubeRadiusChanged" }
        Signal { name: "asynchronousChanged" }
        Signal { name: "statusChanged" }
        Method { name: "doUpdateGeometry" }
        Method { name: "requestFinished" }
    }
}
