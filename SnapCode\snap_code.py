import sys
import json
import os
import re
from datetime import datetime
from typing import Dict, List, Optional

from PySide6.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
    QTabWidget, QSplitter, QGroupBox, QLabel, QComboBox, QTextEdit,
    QPushButton, QLineEdit, QScrollArea, QTreeWidget, QTreeWidgetItem,
    QFileDialog, QMessageBox, QInputDialog, QFrame, QToolBar, QStatusBar,
    QMenuBar, QMenu, QDialog, QDialogButtonBox, QFormLayout, QSpinBox,
    QCheckBox, QProgressBar, QToolButton, QButtonGroup, QRadioButton
)
from PySide6.QtCore import Qt, QTimer, QThread, Signal, QSettings, QSize
from PySide6.QtGui import (
    QAction, QIcon, QFont, QKeySequence, QSyntaxHighlighter,
    QTextCharFormat, QColor, QTextCursor, QPixmap, QPainter
)


class CodeSyntaxHighlighter(QSyntaxHighlighter):
    """Syntax highlighter for code templates"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.highlighting_rules = []
        
        # Placeholder format - Make more prominent
        placeholder_format = QTextCharFormat()
        placeholder_format.setForeground(QColor("#e74c3c"))
        placeholder_format.setBackground(QColor("#fdf2f2"))
        placeholder_format.setFontWeight(QFont.Weight.Bold)
        placeholder_format.setUnderlineStyle(QTextCharFormat.UnderlineStyle.SingleUnderline)
        placeholder_format.setUnderlineColor(QColor("#e74c3c"))
        self.highlighting_rules.append((r'\{\{[^}]+\}\}', placeholder_format))
        
        # Keywords (basic)
        keyword_format = QTextCharFormat()
        keyword_format.setForeground(QColor("#3498db"))
        keyword_format.setFontWeight(QFont.Weight.Bold)
        keywords = ['function', 'const', 'let', 'var', 'class', 'import', 'export', 'return', 'if', 'else', 'for', 'while']
        for keyword in keywords:
            self.highlighting_rules.append((f'\\b{keyword}\\b', keyword_format))
        
        # Strings
        string_format = QTextCharFormat()
        string_format.setForeground(QColor("#27ae60"))
        self.highlighting_rules.append((r'"[^"]*"', string_format))
        self.highlighting_rules.append((r"'[^']*'", string_format))
        
        # Comments
        comment_format = QTextCharFormat()
        comment_format.setForeground(QColor("#95a5a6"))
        comment_format.setFontItalic(True)
        self.highlighting_rules.append((r'//.*', comment_format))
        self.highlighting_rules.append((r'/\*.*\*/', comment_format))
    
    def highlightBlock(self, text):
        for pattern, format in self.highlighting_rules:
            expression = re.compile(pattern)
            for match in expression.finditer(text):
                start, end = match.span()
                self.setFormat(start, end - start, format)


class AdvancedTextEditor(QTextEdit):
    """Advanced text editor with additional features"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setFont(QFont("Consolas", 10))
        self.setTabStopDistance(40)
        self.setAcceptRichText(False)

        # Syntax highlighter
        self.highlighter = CodeSyntaxHighlighter(self.document())

        # Line numbers (simplified)
        self.setLineWrapMode(QTextEdit.LineWrapMode.NoWrap)

        # Context menu for quick placeholder creation
        self.setContextMenuPolicy(Qt.ContextMenuPolicy.CustomContextMenu)
        self.customContextMenuRequested.connect(self.show_context_menu)
        
    def insert_placeholder(self, placeholder_name):
        """Insert a placeholder at cursor position"""
        cursor = self.textCursor()
        cursor.insertText(f"{{{{{placeholder_name}}}}}")
        
    def get_word_under_cursor(self):
        """Get the word under the cursor"""
        cursor = self.textCursor()
        cursor.select(QTextCursor.SelectionType.WordUnderCursor)
        return cursor.selectedText()
    
    def find_and_replace(self, find_text, replace_text, case_sensitive=False):
        """Find and replace text"""
        flags = QTextCursor.FindFlag(0)
        if case_sensitive:
            flags |= QTextCursor.FindFlag.FindCaseSensitively

        cursor = self.document().find(find_text, 0, flags)
        if not cursor.isNull():
            cursor.insertText(replace_text)
            return True
        return False

    def show_context_menu(self, position):
        """Show context menu with placeholder options"""
        cursor = self.textCursor()
        selected_text = cursor.selectedText().strip()

        context_menu = self.createStandardContextMenu()

        if selected_text:
            context_menu.addSeparator()

            # Add "Convert to Placeholder" action
            convert_action = context_menu.addAction("🏷️ Convert to Placeholder")
            convert_action.triggered.connect(lambda: self.convert_selection_to_placeholder())

            # Add "Wrap with Placeholder" action
            wrap_action = context_menu.addAction("📦 Wrap with Custom Placeholder")
            wrap_action.triggered.connect(lambda: self.wrap_selection_with_placeholder())

        # Add "Insert Placeholder" action (always available)
        context_menu.addSeparator()
        insert_action = context_menu.addAction("➕ Insert Placeholder")
        insert_action.triggered.connect(lambda: self.insert_custom_placeholder())

        # Add "Highlight All Placeholders" action
        highlight_action = context_menu.addAction("🔍 Highlight All Placeholders")
        highlight_action.triggered.connect(lambda: self.highlight_all_placeholders())

        context_menu.exec(self.mapToGlobal(position))

    def convert_selection_to_placeholder(self):
        """Convert selected text to a placeholder"""
        cursor = self.textCursor()
        selected_text = cursor.selectedText().strip()

        if not selected_text:
            return

        # Generate placeholder name from selected text
        placeholder_name = self.generate_placeholder_name(selected_text)

        # Ask user for placeholder name
        name, ok = QInputDialog.getText(
            self, "Create Placeholder",
            f"Placeholder name for '{selected_text}':",
            text=placeholder_name
        )

        if ok and name:
            cursor.insertText(f"{{{{{name}}}}}")

    def wrap_selection_with_placeholder(self):
        """Wrap selected text with a custom placeholder"""
        cursor = self.textCursor()
        selected_text = cursor.selectedText().strip()

        if not selected_text:
            return

        # Ask user for placeholder name
        name, ok = QInputDialog.getText(
            self, "Wrap with Placeholder",
            "Enter placeholder name:"
        )

        if ok and name:
            cursor.insertText(f"{{{{{name}_{selected_text.upper()}}}}}")

    def insert_custom_placeholder(self):
        """Insert a custom placeholder at cursor position"""
        name, ok = QInputDialog.getText(
            self, "Insert Placeholder",
            "Enter placeholder name:"
        )

        if ok and name:
            cursor = self.textCursor()
            cursor.insertText(f"{{{{{name}}}}}")

    def generate_placeholder_name(self, text):
        """Generate a placeholder name from text"""
        # Remove special characters and convert to uppercase
        import re
        name = re.sub(r'[^a-zA-Z0-9_]', '_', text)
        name = re.sub(r'_+', '_', name)  # Replace multiple underscores with single
        name = name.strip('_').upper()
        return name or "PLACEHOLDER"

    def highlight_all_placeholders(self):
        """Highlight all placeholders in the text"""
        # This will trigger the syntax highlighter to refresh
        self.highlighter.rehighlight()


class PlaceholderWidget(QWidget):
    """Widget for entering placeholder values"""
    
    def __init__(self, placeholder_name, parent=None):
        super().__init__(parent)
        self.placeholder_name = placeholder_name
        self.setup_ui()
        
    def setup_ui(self):
        layout = QVBoxLayout()
        
        # Label
        label = QLabel(f"{{{{{self.placeholder_name}}}}}")
        label.setFont(QFont("Consolas", 9))
        label.setStyleSheet("color: #e74c3c; font-weight: bold;")
        layout.addWidget(label)
        
        # Input field
        self.input_field = QLineEdit()
        self.input_field.setFont(QFont("Consolas", 9))
        self.input_field.setPlaceholderText(f"Enter value for {self.placeholder_name}")
        layout.addWidget(self.input_field)
        
        # Quick suggestions (if applicable)
        if self.placeholder_name.upper() in ['TYPE', 'DATATYPE', 'VARTYPE']:
            suggestions = QHBoxLayout()
            for suggestion in ['string', 'number', 'boolean', 'object']:
                btn = QPushButton(suggestion)
                btn.setMaximumWidth(60)
                btn.clicked.connect(lambda checked, s=suggestion: self.input_field.setText(s))
                suggestions.addWidget(btn)
            layout.addLayout(suggestions)
        
        self.setLayout(layout)
        
    def get_value(self):
        return self.input_field.text().strip()
    
    def set_value(self, value):
        self.input_field.setText(value)


class PreferencesDialog(QDialog):
    """Preferences dialog for app settings"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("Preferences")
        self.setModal(True)
        self.resize(400, 300)
        self.setup_ui()
        
    def setup_ui(self):
        layout = QVBoxLayout()
        
        # Editor settings
        editor_group = QGroupBox("Editor Settings")
        editor_layout = QFormLayout()
        
        self.font_size_spin = QSpinBox()
        self.font_size_spin.setRange(8, 24)
        self.font_size_spin.setValue(10)
        editor_layout.addRow("Font Size:", self.font_size_spin)
        
        self.auto_save_check = QCheckBox("Auto-save generated code")
        editor_layout.addRow(self.auto_save_check)
        
        self.syntax_highlight_check = QCheckBox("Enable syntax highlighting")
        self.syntax_highlight_check.setChecked(True)
        editor_layout.addRow(self.syntax_highlight_check)
        
        editor_group.setLayout(editor_layout)
        layout.addWidget(editor_group)
        
        # Generation settings
        gen_group = QGroupBox("Code Generation")
        gen_layout = QFormLayout()
        
        self.default_extension = QComboBox()
        self.default_extension.addItems(['.js', '.ts', '.py', '.jsx', '.tsx', '.html', '.css'])
        gen_layout.addRow("Default file extension:", self.default_extension)
        
        self.auto_format_check = QCheckBox("Auto-format generated code")
        gen_layout.addRow(self.auto_format_check)
        
        gen_group.setLayout(gen_layout)
        layout.addWidget(gen_group)
        
        # Buttons
        button_box = QDialogButtonBox(QDialogButtonBox.StandardButton.Ok | QDialogButtonBox.StandardButton.Cancel)
        button_box.accepted.connect(self.accept)
        button_box.rejected.connect(self.reject)
        layout.addWidget(button_box)
        
        self.setLayout(layout)


class CodeGeneratorApp(QMainWindow):
    """Main application class"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Advanced Code Generator")
        self.setGeometry(100, 100, 1400, 900)
        
        # Data storage
        self.boilerplates = {}
        self.current_placeholders = {}
        self.generated_code = ""
        self.boilerplates_file = "boilerplates.json"
        self.settings = QSettings("CodeGenerator", "AdvancedCodeGen")
        
        # Load saved boilerplates
        self.load_boilerplates()
        
        # Setup UI
        self.setup_ui()
        self.setup_menu_bar()
        self.setup_tool_bar()
        self.setup_status_bar()
        
        # Load settings
        self.load_settings()
        
        # Auto-save timer
        self.auto_save_timer = QTimer()
        self.auto_save_timer.timeout.connect(self.auto_save_boilerplates)
        self.auto_save_timer.start(30000)  # Auto-save every 30 seconds
        
    def setup_ui(self):
        """Setup the main UI"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout()
        central_widget.setLayout(layout)
        
        # Create tab widget
        self.tab_widget = QTabWidget()
        layout.addWidget(self.tab_widget)
        
        # Create tabs
        self.create_generator_tab()
        self.create_manage_tab()
        self.create_template_library_tab()
        
    def setup_menu_bar(self):
        """Setup menu bar"""
        menubar = self.menuBar()
        
        # File menu
        file_menu = menubar.addMenu("&File")
        
        new_action = QAction("&New Template", self)
        new_action.setShortcut(QKeySequence.StandardKey.New)
        new_action.triggered.connect(self.new_template)
        file_menu.addAction(new_action)
        
        open_action = QAction("&Open Template", self)
        open_action.setShortcut(QKeySequence.StandardKey.Open)
        open_action.triggered.connect(self.load_code_file)
        file_menu.addAction(open_action)
        
        save_action = QAction("&Save Template", self)
        save_action.setShortcut(QKeySequence.StandardKey.Save)
        save_action.triggered.connect(self.save_boilerplate)
        file_menu.addAction(save_action)
        
        file_menu.addSeparator()
        
        exit_action = QAction("E&xit", self)
        exit_action.setShortcut(QKeySequence.StandardKey.Quit)
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)
        
        # Edit menu
        edit_menu = menubar.addMenu("&Edit")
        
        undo_action = QAction("&Undo", self)
        undo_action.setShortcut(QKeySequence.StandardKey.Undo)
        undo_action.triggered.connect(lambda: self.code_editor.undo())
        edit_menu.addAction(undo_action)
        
        redo_action = QAction("&Redo", self)
        redo_action.setShortcut(QKeySequence.StandardKey.Redo)
        redo_action.triggered.connect(lambda: self.code_editor.redo())
        edit_menu.addAction(redo_action)
        
        edit_menu.addSeparator()
        
        find_action = QAction("&Find", self)
        find_action.setShortcut(QKeySequence.StandardKey.Find)
        find_action.triggered.connect(self.show_find_dialog)
        edit_menu.addAction(find_action)

        edit_menu.addSeparator()

        # Placeholder actions
        quick_placeholder_action = QAction("Quick &Placeholder", self)
        quick_placeholder_action.setShortcut(QKeySequence("Ctrl+Shift+P"))
        quick_placeholder_action.setToolTip("Convert selected text to placeholder (Ctrl+Shift+P)")
        quick_placeholder_action.triggered.connect(self.quick_placeholder_from_selection)
        edit_menu.addAction(quick_placeholder_action)

        insert_placeholder_action = QAction("&Insert Placeholder", self)
        insert_placeholder_action.setShortcut(QKeySequence("Ctrl+Alt+P"))
        insert_placeholder_action.setToolTip("Insert new placeholder at cursor (Ctrl+Alt+P)")
        insert_placeholder_action.triggered.connect(self.insert_placeholder_at_cursor)
        edit_menu.addAction(insert_placeholder_action)

        highlight_placeholders_action = QAction("&Highlight Placeholders", self)
        highlight_placeholders_action.setShortcut(QKeySequence("Ctrl+H"))
        highlight_placeholders_action.triggered.connect(self.highlight_all_placeholders)
        edit_menu.addAction(highlight_placeholders_action)
        
        # Tools menu
        tools_menu = menubar.addMenu("&Tools")
        
        preferences_action = QAction("&Preferences", self)
        preferences_action.triggered.connect(self.show_preferences)
        tools_menu.addAction(preferences_action)
        
        # Help menu
        help_menu = menubar.addMenu("&Help")
        
        about_action = QAction("&About", self)
        about_action.triggered.connect(self.show_about)
        help_menu.addAction(about_action)
        
    def setup_tool_bar(self):
        """Setup toolbar"""
        toolbar = QToolBar()
        self.addToolBar(toolbar)
        
        # New template
        new_btn = QAction("New", self)
        new_btn.setToolTip("Create new template")
        new_btn.triggered.connect(self.new_template)
        toolbar.addAction(new_btn)
        
        # Open template
        open_btn = QAction("Open", self)
        open_btn.setToolTip("Open template file")
        open_btn.triggered.connect(self.load_code_file)
        toolbar.addAction(open_btn)
        
        # Save template
        save_btn = QAction("Save", self)
        save_btn.setToolTip("Save as boilerplate")
        save_btn.triggered.connect(self.save_boilerplate)
        toolbar.addAction(save_btn)
        
        toolbar.addSeparator()
        
        # Generate code
        generate_btn = QAction("Generate", self)
        generate_btn.setToolTip("Generate code")
        generate_btn.triggered.connect(self.generate_code)
        toolbar.addAction(generate_btn)
        
        # Save generated code
        save_gen_btn = QAction("Save Generated", self)
        save_gen_btn.setToolTip("Save generated code")
        save_gen_btn.triggered.connect(self.save_generated_code)
        toolbar.addAction(save_gen_btn)
        
    def setup_status_bar(self):
        """Setup status bar"""
        self.status_bar = QStatusBar()
        self.setStatusBar(self.status_bar)
        
        self.status_bar.showMessage("Ready")
        
        # Add permanent widgets
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        self.status_bar.addPermanentWidget(self.progress_bar)
        
    def create_generator_tab(self):
        """Create the main code generator tab"""
        generator_widget = QWidget()
        self.tab_widget.addTab(generator_widget, "Code Generator")
        
        layout = QHBoxLayout()
        generator_widget.setLayout(layout)
        
        # Create splitter
        splitter = QSplitter(Qt.Orientation.Horizontal)
        layout.addWidget(splitter)
        
        # Left panel
        left_panel = QWidget()
        left_layout = QVBoxLayout()
        left_panel.setLayout(left_layout)
        
        # Boilerplate selection
        boilerplate_group = QGroupBox("Template Selection")
        boilerplate_layout = QVBoxLayout()
        
        self.boilerplate_combo = QComboBox()
        self.boilerplate_combo.addItems(list(self.boilerplates.keys()))
        self.boilerplate_combo.currentTextChanged.connect(self.on_boilerplate_select)
        boilerplate_layout.addWidget(QLabel("Select Template:"))
        boilerplate_layout.addWidget(self.boilerplate_combo)
        
        boilerplate_group.setLayout(boilerplate_layout)
        left_layout.addWidget(boilerplate_group)
        
        # Code editor
        editor_group = QGroupBox("Code Template")
        editor_layout = QVBoxLayout()
        
        # Instructions
        instructions = QLabel("💡 Use {{PLACEHOLDER}} format in your code (e.g., {{SLICE_NAME}}, {{ENTITY_NAME}})")
        instructions.setStyleSheet("color: #3498db; font-weight: bold;")
        editor_layout.addWidget(instructions)

        # Quick help for placeholder creation
        help_text = QLabel("🚀 Quick Tips: Select text → Right-click → 'Convert to Placeholder' | Ctrl+Shift+P for quick conversion")
        help_text.setStyleSheet("color: #27ae60; font-size: 10px; font-style: italic;")
        help_text.setWordWrap(True)
        editor_layout.addWidget(help_text)
        
        # Text editor
        self.code_editor = AdvancedTextEditor()
        self.code_editor.textChanged.connect(self.on_code_change)
        editor_layout.addWidget(self.code_editor)
        
        # Editor buttons
        editor_buttons = QHBoxLayout()

        load_btn = QPushButton("📁 Load from File")
        load_btn.clicked.connect(self.load_code_file)
        editor_buttons.addWidget(load_btn)

        save_template_btn = QPushButton("💾 Save as Template")
        save_template_btn.clicked.connect(self.save_boilerplate)
        editor_buttons.addWidget(save_template_btn)

        # Quick placeholder button
        quick_placeholder_btn = QPushButton("🏷️ Quick Placeholder")
        quick_placeholder_btn.setToolTip("Select text and click to convert to placeholder")
        quick_placeholder_btn.clicked.connect(self.quick_placeholder_from_selection)
        editor_buttons.addWidget(quick_placeholder_btn)

        parse_btn = QPushButton("🔄 Parse Placeholders")
        parse_btn.clicked.connect(self.parse_placeholders)
        editor_buttons.addWidget(parse_btn)
        
        editor_layout.addLayout(editor_buttons)
        editor_group.setLayout(editor_layout)
        left_layout.addWidget(editor_group)
        
        splitter.addWidget(left_panel)
        
        # Right panel
        right_panel = QWidget()
        right_layout = QVBoxLayout()
        right_panel.setLayout(right_layout)
        
        # Placeholder form
        form_group = QGroupBox("Placeholder Values")
        form_layout = QVBoxLayout()
        
        self.placeholder_scroll = QScrollArea()
        self.placeholder_scroll.setWidgetResizable(True)
        self.placeholder_widget = QWidget()
        self.placeholder_layout = QVBoxLayout()
        self.placeholder_widget.setLayout(self.placeholder_layout)
        self.placeholder_scroll.setWidget(self.placeholder_widget)
        
        form_layout.addWidget(self.placeholder_scroll)
        form_group.setLayout(form_layout)
        right_layout.addWidget(form_group)
        
        # Generate button
        self.generate_btn = QPushButton("🚀 Generate Code")
        self.generate_btn.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                padding: 10px;
                font-size: 14px;
                font-weight: bold;
                border-radius: 5px;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)
        self.generate_btn.clicked.connect(self.generate_code)
        right_layout.addWidget(self.generate_btn)
        
        # Preview area
        preview_group = QGroupBox("Generated Code Preview")
        preview_layout = QVBoxLayout()
        
        self.preview_editor = QTextEdit()
        self.preview_editor.setFont(QFont("Consolas", 9))
        self.preview_editor.setReadOnly(True)
        self.preview_editor.setStyleSheet("""
            QTextEdit {
                background-color: #f8f9fa;
                color: #2c3e50;
                border: 1px solid #dee2e6;
                border-radius: 4px;
                padding: 8px;
                selection-background-color: #3498db;
                selection-color: white;
            }
        """)
        preview_layout.addWidget(self.preview_editor)
        
        # Save buttons - Made more prominent
        save_buttons = QHBoxLayout()
        
        # Main save button
        self.save_generated_btn = QPushButton("💾 Save Generated Code")
        self.save_generated_btn.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border: none;
                padding: 8px 16px;
                font-size: 12px;
                font-weight: bold;
                border-radius: 4px;
            }
            QPushButton:hover {
                background-color: #219a52;
            }
        """)
        self.save_generated_btn.clicked.connect(self.save_generated_code)
        save_buttons.addWidget(self.save_generated_btn)
        
        # Copy button
        copy_btn = QPushButton("📋 Copy to Clipboard")
        copy_btn.clicked.connect(self.copy_to_clipboard)
        save_buttons.addWidget(copy_btn)
        
        # Save as button
        save_as_btn = QPushButton("📁 Save As...")
        save_as_btn.clicked.connect(self.save_as_dialog)
        save_buttons.addWidget(save_as_btn)
        
        preview_layout.addLayout(save_buttons)
        preview_group.setLayout(preview_layout)
        right_layout.addWidget(preview_group)
        
        splitter.addWidget(right_panel)
        
        # Set splitter proportions
        splitter.setSizes([600, 600])
        
    def create_manage_tab(self):
        """Create the manage boilerplates tab"""
        manage_widget = QWidget()
        self.tab_widget.addTab(manage_widget, "Manage Templates")
        
        layout = QVBoxLayout()
        manage_widget.setLayout(layout)
        
        # Tree widget for boilerplates
        self.tree_widget = QTreeWidget()
        self.tree_widget.setHeaderLabels(["Template Name", "Created", "Placeholders", "Size"])
        self.tree_widget.setAlternatingRowColors(True)
        layout.addWidget(self.tree_widget)
        
        # Buttons
        button_layout = QHBoxLayout()
        
        edit_btn = QPushButton("✏️ Edit")
        edit_btn.clicked.connect(self.edit_boilerplate)
        button_layout.addWidget(edit_btn)
        
        delete_btn = QPushButton("🗑️ Delete")
        delete_btn.clicked.connect(self.delete_boilerplate)
        button_layout.addWidget(delete_btn)
        
        duplicate_btn = QPushButton("📋 Duplicate")
        duplicate_btn.clicked.connect(self.duplicate_boilerplate)
        button_layout.addWidget(duplicate_btn)
        
        export_btn = QPushButton("📤 Export")
        export_btn.clicked.connect(self.export_boilerplate)
        button_layout.addWidget(export_btn)
        
        import_btn = QPushButton("📥 Import")
        import_btn.clicked.connect(self.import_boilerplate)
        button_layout.addWidget(import_btn)
        
        refresh_btn = QPushButton("🔄 Refresh")
        refresh_btn.clicked.connect(self.refresh_boilerplate_list)
        button_layout.addWidget(refresh_btn)
        
        layout.addLayout(button_layout)
        
        # Populate tree
        self.refresh_boilerplate_list()
        
    def create_template_library_tab(self):
        """Create template library tab with common templates"""
        library_widget = QWidget()
        self.tab_widget.addTab(library_widget, "Template Library")
        
        layout = QVBoxLayout()
        library_widget.setLayout(layout)
        
        # Built-in templates
        templates_group = QGroupBox("Built-in Templates")
        templates_layout = QVBoxLayout()
        
        # Template categories
        categories = {
            "React Components": [
                ("Functional Component", self.create_react_component_template),
                ("Class Component", self.create_react_class_template),
                ("Custom Hook", self.create_react_hook_template),
            ],
            "Node.js/Express": [
                ("Express Route", self.create_express_route_template),
                ("Express Middleware", self.create_express_middleware_template),
                ("MongoDB Schema", self.create_mongodb_schema_template),
            ],
            "Python": [
                ("Class Template", self.create_python_class_template),
                ("Function Template", self.create_python_function_template),
                ("Flask Route", self.create_flask_route_template),
            ],
            "HTML/CSS": [
                ("HTML Page", self.create_html_page_template),
                ("CSS Component", self.create_css_component_template),
            ]
        }
        
        for category, templates in categories.items():
            category_group = QGroupBox(category)
            category_layout = QVBoxLayout()
            
            for name, handler in templates:
                btn = QPushButton(name)
                btn.clicked.connect(handler)
                category_layout.addWidget(btn)
            
            category_group.setLayout(category_layout)
            templates_layout.addWidget(category_group)
        
        templates_group.setLayout(templates_layout)
        layout.addWidget(templates_group)
        
    def new_template(self):
        """Create a new template"""
        self.code_editor.clear()
        self.placeholder_layout.removeWidget(self.placeholder_widget)
        self.placeholder_widget = QWidget()
        self.placeholder_layout = QVBoxLayout()
        self.placeholder_widget.setLayout(self.placeholder_layout)
        self.placeholder_scroll.setWidget(self.placeholder_widget)
        
    def load_code_file(self):
        """Load code from file"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "Open Code File", "",
            "JavaScript (*.js);;TypeScript (*.ts);;Python (*.py);;React (*.jsx);;All Files (*)"
        )
        
        if file_path:
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                    self.code_editor.setPlainText(content)
                    self.parse_placeholders()
                    self.status_bar.showMessage(f"Loaded: {file_path}")
            except Exception as e:
                QMessageBox.critical(self, "Error", f"Failed to load file: {str(e)}")
                
    def save_boilerplate(self):
        """Save current code as boilerplate"""
        code = self.code_editor.toPlainText().strip()
        if not code:
            QMessageBox.warning(self, "Warning", "No code to save!")
            return
            
        name, ok = QInputDialog.getText(self, "Save Template", "Enter template name:")
        if not ok or not name:
            return
            
        # Extract placeholders
        placeholders = re.findall(r'\{\{([^}]+)\}\}', code)
        unique_placeholders = list(set(placeholders))
        
        # Save boilerplate
        self.boilerplates[name] = {
            'code': code,
            'created': datetime.now().isoformat(),
            'placeholders': unique_placeholders,
            'size': len(code)
        }
        
        self.save_boilerplates()
        self.boilerplate_combo.clear()
        self.boilerplate_combo.addItems(list(self.boilerplates.keys()))
        self.boilerplate_combo.setCurrentText(name)
        self.refresh_boilerplate_list()
        
        QMessageBox.information(self, "Success", f"Template '{name}' saved successfully!")
        self.status_bar.showMessage(f"Saved template: {name}")
        
    def on_boilerplate_select(self, name):
        """Handle boilerplate selection"""
        if name and name in self.boilerplates:
            self.code_editor.setPlainText(self.boilerplates[name]['code'])
            self.parse_placeholders()
            
    def on_code_change(self):
        """Handle code text changes"""
        # Debounced parsing
        if hasattr(self, '_parse_timer'):
            self._parse_timer.stop()
        
        self._parse_timer = QTimer()
        self._parse_timer.timeout.connect(self.parse_placeholders)
        self._parse_timer.setSingleShot(True)
        self._parse_timer.start(1000)  # 1 second delay
        
    def quick_placeholder_from_selection(self):
        """Create placeholder from selected text in editor"""
        cursor = self.code_editor.textCursor()
        selected_text = cursor.selectedText().strip()

        if not selected_text:
            QMessageBox.information(
                self, "No Selection",
                "Please select text in the editor first, then click this button to convert it to a placeholder."
            )
            return

        # Generate placeholder name from selected text
        placeholder_name = self.code_editor.generate_placeholder_name(selected_text)

        # Ask user for placeholder name
        name, ok = QInputDialog.getText(
            self, "Create Placeholder",
            f"Placeholder name for '{selected_text}':",
            text=placeholder_name
        )

        if ok and name:
            cursor.insertText(f"{{{{{name}}}}}")
            # Auto-parse placeholders after creation
            self.parse_placeholders()
            self.status_bar.showMessage(f"Created placeholder: {{{{{name}}}}}")

            # Flash the placeholder for visual feedback
            QTimer.singleShot(100, lambda: self.code_editor.highlight_all_placeholders())

    def insert_placeholder_at_cursor(self):
        """Insert a new placeholder at cursor position"""
        name, ok = QInputDialog.getText(
            self, "Insert Placeholder",
            "Enter placeholder name:"
        )

        if ok and name:
            cursor = self.code_editor.textCursor()
            cursor.insertText(f"{{{{{name}}}}}")
            # Auto-parse placeholders after creation
            self.parse_placeholders()
            self.status_bar.showMessage(f"Inserted placeholder: {{{{{name}}}}}")

            # Flash the placeholder for visual feedback
            QTimer.singleShot(100, lambda: self.code_editor.highlight_all_placeholders())

    def highlight_all_placeholders(self):
        """Highlight all placeholders in the editor"""
        self.code_editor.highlight_all_placeholders()

    def parse_placeholders(self):
        """Parse placeholders from code and create form"""
        code = self.code_editor.toPlainText()
        placeholders = re.findall(r'\{\{([^}]+)\}\}', code)
        unique_placeholders = list(set(placeholders))

        # Clear existing form
        for i in reversed(range(self.placeholder_layout.count())):
            child = self.placeholder_layout.itemAt(i).widget()
            if child:
                child.deleteLater()

        self.current_placeholders = {}

        if not unique_placeholders:
            label = QLabel("No placeholders found.\nUse {{PLACEHOLDER}} format in your code.")
            label.setStyleSheet("color: #7f8c8d; font-style: italic;")
            label.setAlignment(Qt.AlignmentFlag.AlignCenter)
            self.placeholder_layout.addWidget(label)
            return

        # Create form fields
        for placeholder in unique_placeholders:
            widget = PlaceholderWidget(placeholder)
            self.placeholder_layout.addWidget(widget)
            self.current_placeholders[placeholder] = widget

            # Connect Enter key to generate
            widget.input_field.returnPressed.connect(self.generate_code)

        # Update status bar with placeholder count
        self.status_bar.showMessage(f"Found {len(unique_placeholders)} placeholder(s): {', '.join(unique_placeholders)}")
            
    def generate_code(self):
        """Generate code by replacing placeholders"""
        code = self.code_editor.toPlainText().strip()
        if not code:
            QMessageBox.warning(self, "Warning", "No code template provided!")
            return
            
        if not self.current_placeholders:
            QMessageBox.warning(self, "Warning", "No placeholders found!")
            return
            
        # Check for empty values
        for placeholder, widget in self.current_placeholders.items():
            if not widget.get_value():
                QMessageBox.warning(self, "Warning", f"Please provide a value for {{{{{placeholder}}}}}")
                widget.input_field.setFocus()
                return
                
        # Replace placeholders
        generated = code
        for placeholder, widget in self.current_placeholders.items():
            value = widget.get_value()
            generated = generated.replace(f"{{{{{placeholder}}}}}", value)
            
        self.generated_code = generated
        self.preview_editor.setPlainText(generated)
        
        # Show success message
        self.status_bar.showMessage("Code generated successfully!")
        
        # Auto-save if enabled
        if self.settings.value("auto_save", False, type=bool):
            self.quick_save()
        
    def save_generated_code(self):
        """Save generated code to file"""
        if not self.generated_code:
            QMessageBox.warning(self, "Warning", "No generated code to save!")
            return
            
        reply = QMessageBox.question(
            self, "Save Options",
            "Choose save option:\n\nYes - Quick save with auto-generated filename\nNo - Choose custom filename and location",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No | QMessageBox.StandardButton.Cancel
        )
        
        if reply == QMessageBox.StandardButton.Yes:
            self.quick_save()
        elif reply == QMessageBox.StandardButton.No:
            self.save_as_dialog()
            
    def quick_save(self):
        """Quick save with auto-generated filename"""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            extension = self.settings.value("default_extension", ".js", type=str)
            filename = f"generated_code_{timestamp}{extension}"
            
            # Create 'generated' directory if it doesn't exist
            if not os.path.exists('generated'):
                os.makedirs('generated')
                
            file_path = os.path.join('generated', filename)
            
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(self.generated_code)
                
            QMessageBox.information(self, "Success", f"Code saved to:\n{file_path}")
            self.status_bar.showMessage(f"Saved: {file_path}")
            
        except Exception as e:
            QMessageBox.critical(self, "Error", f"Failed to save file: {str(e)}")
            
    def save_as_dialog(self):
        """Save generated code with custom filename and location"""
        if not self.generated_code:
            QMessageBox.warning(self, "Warning", "No generated code to save!")
            return
            
        file_path, _ = QFileDialog.getSaveFileName(
            self, "Save Generated Code", "",
            "JavaScript (*.js);;TypeScript (*.ts);;Python (*.py);;React (*.jsx);;React TypeScript (*.tsx);;HTML (*.html);;CSS (*.css);;All Files (*)"
        )
        
        if file_path:
            try:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(self.generated_code)
                QMessageBox.information(self, "Success", f"Code saved to:\n{file_path}")
                self.status_bar.showMessage(f"Saved: {file_path}")
            except Exception as e:
                QMessageBox.critical(self, "Error", f"Failed to save file: {str(e)}")
                
    def copy_to_clipboard(self):
        """Copy generated code to clipboard"""
        if not self.generated_code:
            QMessageBox.warning(self, "Warning", "No generated code to copy!")
            return
            
        clipboard = QApplication.clipboard()
        clipboard.setText(self.generated_code)
        QMessageBox.information(self, "Success", "Code copied to clipboard!")
        self.status_bar.showMessage("Code copied to clipboard")
        
    def refresh_boilerplate_list(self):
        """Refresh the boilerplate list in manage tab"""
        self.tree_widget.clear()
        
        for name, data in self.boilerplates.items():
            item = QTreeWidgetItem()
            item.setText(0, name)
            
            # Format creation date
            created = data.get('created', 'Unknown')
            if created != 'Unknown':
                try:
                    created_date = datetime.fromisoformat(created)
                    created = created_date.strftime('%Y-%m-%d %H:%M')
                except:
                    pass
            item.setText(1, created)
            
            # Placeholders
            placeholders = ', '.join(data.get('placeholders', []))
            item.setText(2, placeholders)
            
            # Size
            size = data.get('size', len(data.get('code', '')))
            item.setText(3, f"{size} chars")
            
            self.tree_widget.addTopLevelItem(item)
            
    def edit_boilerplate(self):
        """Edit selected boilerplate"""
        current_item = self.tree_widget.currentItem()
        if not current_item:
            QMessageBox.warning(self, "Warning", "Please select a template to edit!")
            return
            
        name = current_item.text(0)
        if name in self.boilerplates:
            # Switch to generator tab and load the boilerplate
            self.tab_widget.setCurrentIndex(0)
            self.boilerplate_combo.setCurrentText(name)
            self.on_boilerplate_select(name)
            
    def delete_boilerplate(self):
        """Delete selected boilerplate"""
        current_item = self.tree_widget.currentItem()
        if not current_item:
            QMessageBox.warning(self, "Warning", "Please select a template to delete!")
            return
            
        name = current_item.text(0)
        
        reply = QMessageBox.question(
            self, "Confirm Delete",
            f"Are you sure you want to delete '{name}'?",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
        )
        
        if reply == QMessageBox.StandardButton.Yes:
            if name in self.boilerplates:
                del self.boilerplates[name]
                self.save_boilerplates()
                self.refresh_boilerplate_list()
                
                # Update combo box
                self.boilerplate_combo.clear()
                self.boilerplate_combo.addItems(list(self.boilerplates.keys()))
                
                QMessageBox.information(self, "Success", f"Template '{name}' deleted successfully!")
                self.status_bar.showMessage(f"Deleted template: {name}")
                
    def duplicate_boilerplate(self):
        """Duplicate selected boilerplate"""
        current_item = self.tree_widget.currentItem()
        if not current_item:
            QMessageBox.warning(self, "Warning", "Please select a template to duplicate!")
            return
            
        name = current_item.text(0)
        if name in self.boilerplates:
            new_name, ok = QInputDialog.getText(self, "Duplicate Template", "Enter new template name:", text=f"{name}_copy")
            if ok and new_name:
                self.boilerplates[new_name] = self.boilerplates[name].copy()
                self.boilerplates[new_name]['created'] = datetime.now().isoformat()
                self.save_boilerplates()
                self.refresh_boilerplate_list()
                
                # Update combo box
                self.boilerplate_combo.clear()
                self.boilerplate_combo.addItems(list(self.boilerplates.keys()))
                
                QMessageBox.information(self, "Success", f"Template duplicated as '{new_name}'!")
                
    def export_boilerplate(self):
        """Export selected boilerplate to file"""
        current_item = self.tree_widget.currentItem()
        if not current_item:
            QMessageBox.warning(self, "Warning", "Please select a template to export!")
            return
            
        name = current_item.text(0)
        if name in self.boilerplates:
            file_path, _ = QFileDialog.getSaveFileName(
                self, "Export Template", f"{name}.json",
                "JSON files (*.json);;All Files (*)"
            )
            
            if file_path:
                try:
                    with open(file_path, 'w', encoding='utf-8') as f:
                        json.dump({name: self.boilerplates[name]}, f, indent=2, ensure_ascii=False)
                    QMessageBox.information(self, "Success", f"Template exported to:\n{file_path}")
                except Exception as e:
                    QMessageBox.critical(self, "Error", f"Failed to export template: {str(e)}")
                    
    def import_boilerplate(self):
        """Import boilerplate from file"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "Import Template", "",
            "JSON files (*.json);;All Files (*)"
        )
        
        if file_path:
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    imported_data = json.load(f)
                    
                for name, data in imported_data.items():
                    if name in self.boilerplates:
                        reply = QMessageBox.question(
                            self, "Template Exists",
                            f"Template '{name}' already exists. Overwrite?",
                            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
                        )
                        if reply == QMessageBox.StandardButton.No:
                            continue
                            
                    self.boilerplates[name] = data
                    
                self.save_boilerplates()
                self.refresh_boilerplate_list()
                
                # Update combo box
                self.boilerplate_combo.clear()
                self.boilerplate_combo.addItems(list(self.boilerplates.keys()))
                
                QMessageBox.information(self, "Success", "Templates imported successfully!")
                
            except Exception as e:
                QMessageBox.critical(self, "Error", f"Failed to import template: {str(e)}")
                
    def load_boilerplates(self):
        """Load boilerplates from JSON file"""
        try:
            if os.path.exists(self.boilerplates_file):
                with open(self.boilerplates_file, 'r', encoding='utf-8') as f:
                    self.boilerplates = json.load(f)
        except Exception as e:
            QMessageBox.critical(self, "Error", f"Failed to load boilerplates: {str(e)}")
            
    def save_boilerplates(self):
        """Save boilerplates to JSON file"""
        try:
            with open(self.boilerplates_file, 'w', encoding='utf-8') as f:
                json.dump(self.boilerplates, f, indent=2, ensure_ascii=False)
        except Exception as e:
            QMessageBox.critical(self, "Error", f"Failed to save boilerplates: {str(e)}")
            
    def auto_save_boilerplates(self):
        """Auto-save boilerplates"""
        self.save_boilerplates()
        
    def show_find_dialog(self):
        """Show find and replace dialog"""
        find_text, ok = QInputDialog.getText(self, "Find", "Find what:")
        if ok and find_text:
            if not self.code_editor.find(find_text):
                QMessageBox.information(self, "Find", "Text not found!")
                
    def show_preferences(self):
        """Show preferences dialog"""
        dialog = PreferencesDialog(self)
        if dialog.exec() == QDialog.DialogCode.Accepted:
            # Apply preferences
            self.settings.setValue("font_size", dialog.font_size_spin.value())
            self.settings.setValue("auto_save", dialog.auto_save_check.isChecked())
            self.settings.setValue("syntax_highlight", dialog.syntax_highlight_check.isChecked())
            self.settings.setValue("default_extension", dialog.default_extension.currentText())
            self.settings.setValue("auto_format", dialog.auto_format_check.isChecked())
            
            # Apply font size
            font = self.code_editor.font()
            font.setPointSize(dialog.font_size_spin.value())
            self.code_editor.setFont(font)
            
    def show_about(self):
        """Show about dialog"""
        QMessageBox.about(self, "About", 
                         "Advanced Code Generator v2.0\n\n"
                         "A powerful tool for generating code from templates\n"
                         "with placeholder replacement.\n\n"
                         "Built with PySide6")
        
    def load_settings(self):
        """Load application settings"""
        font_size = self.settings.value("font_size", 10, type=int)
        font = self.code_editor.font()
        font.setPointSize(font_size)
        self.code_editor.setFont(font)
        
    def closeEvent(self, event):
        """Handle application close event"""
        self.save_boilerplates()
        event.accept()
        
    # Built-in template generators
    def create_react_component_template(self):
        """Create React functional component template"""
        template = """import React from 'react';
import './{{COMPONENT_NAME}}.css';

interface {{COMPONENT_NAME}}Props {
  {{PROP_NAME}}: {{PROP_TYPE}};
}

const {{COMPONENT_NAME}}: React.FC<{{COMPONENT_NAME}}Props> = ({ {{PROP_NAME}} }) => {
  return (
    <div className="{{COMPONENT_NAME_LOWER}}">
      <h2>{{COMPONENT_TITLE}}</h2>
      <p>{{COMPONENT_DESCRIPTION}}</p>
    </div>
  );
};

export default {{COMPONENT_NAME}};"""
        
        self.code_editor.setPlainText(template)
        self.tab_widget.setCurrentIndex(0)
        self.parse_placeholders()
        
    def create_react_class_template(self):
        """Create React class component template"""
        template = """import React, { Component } from 'react';
import './{{COMPONENT_NAME}}.css';

interface {{COMPONENT_NAME}}Props {
  {{PROP_NAME}}: {{PROP_TYPE}};
}

interface {{COMPONENT_NAME}}State {
  {{STATE_NAME}}: {{STATE_TYPE}};
}

class {{COMPONENT_NAME}} extends Component<{{COMPONENT_NAME}}Props, {{COMPONENT_NAME}}State> {
  constructor(props: {{COMPONENT_NAME}}Props) {
    super(props);
    this.state = {
      {{STATE_NAME}}: {{STATE_INITIAL_VALUE}}
    };
  }

  render() {
    return (
      <div className="{{COMPONENT_NAME_LOWER}}">
        <h2>{{COMPONENT_TITLE}}</h2>
        <p>{{COMPONENT_DESCRIPTION}}</p>
      </div>
    );
  }
}

export default {{COMPONENT_NAME}};"""
        
        self.code_editor.setPlainText(template)
        self.tab_widget.setCurrentIndex(0)
        self.parse_placeholders()
        
    def create_react_hook_template(self):
        """Create React custom hook template"""
        template = """import { useState, useEffect } from 'react';

interface {{HOOK_NAME}}Options {
  {{OPTION_NAME}}: {{OPTION_TYPE}};
}

interface {{HOOK_NAME}}Return {
  {{RETURN_NAME}}: {{RETURN_TYPE}};
  {{ACTION_NAME}}: ({{PARAM_NAME}}: {{PARAM_TYPE}}) => void;
}

export const {{HOOK_NAME}} = (options: {{HOOK_NAME}}Options): {{HOOK_NAME}}Return => {
  const [{{STATE_NAME}}, set{{STATE_NAME_CAPITALIZED}}] = useState<{{STATE_TYPE}}>({{INITIAL_VALUE}});

  useEffect(() => {
    // {{EFFECT_DESCRIPTION}}
  }, [{{DEPENDENCY}}]);

  const {{ACTION_NAME}} = ({{PARAM_NAME}}: {{PARAM_TYPE}}) => {
    // {{ACTION_DESCRIPTION}}
    set{{STATE_NAME_CAPITALIZED}}({{NEW_VALUE}});
  };

  return {
    {{RETURN_NAME}}: {{STATE_NAME}},
    {{ACTION_NAME}}
  };
};"""
        
        self.code_editor.setPlainText(template)
        self.tab_widget.setCurrentIndex(0)
        self.parse_placeholders()
        
    def create_express_route_template(self):
        """Create Express route template"""
        template = """import express from 'express';
import { {{CONTROLLER_NAME}} } from '../controllers/{{CONTROLLER_FILE}}';
import { {{MIDDLEWARE_NAME}} } from '../middleware/{{MIDDLEWARE_FILE}}';

const router = express.Router();

// {{ROUTE_DESCRIPTION}}
router.{{HTTP_METHOD}}('{{ROUTE_PATH}}', {{MIDDLEWARE_NAME}}, async (req, res) => {
  try {
    const {{PARAM_NAME}} = req.{{PARAM_SOURCE}}.{{PARAM_KEY}};
    
    const result = await {{CONTROLLER_NAME}}.{{ACTION_NAME}}({{PARAM_NAME}});
    
    res.status({{SUCCESS_STATUS}}).json({
      success: true,
      data: result,
      message: '{{SUCCESS_MESSAGE}}'
    });
  } catch (error) {
    res.status({{ERROR_STATUS}}).json({
      success: false,
      message: error.message || '{{ERROR_MESSAGE}}'
    });
  }
});

export default router;"""
        
        self.code_editor.setPlainText(template)
        self.tab_widget.setCurrentIndex(0)
        self.parse_placeholders()
        
    def create_express_middleware_template(self):
        """Create Express middleware template"""
        template = """import { Request, Response, NextFunction } from 'express';

interface {{MIDDLEWARE_NAME}}Options {
  {{OPTION_NAME}}: {{OPTION_TYPE}};
}

export const {{MIDDLEWARE_NAME}} = (options: {{MIDDLEWARE_NAME}}Options) => {
  return (req: Request, res: Response, next: NextFunction) => {
    try {
      // {{MIDDLEWARE_DESCRIPTION}}
      const {{VARIABLE_NAME}} = req.{{SOURCE}}.{{KEY}};
      
      if (!{{VARIABLE_NAME}}) {
        return res.status({{ERROR_STATUS}}).json({
          success: false,
          message: '{{ERROR_MESSAGE}}'
        });
      }
      
      // {{VALIDATION_LOGIC}}
      req.{{ATTACH_TO}} = {{VARIABLE_NAME}};
      next();
    } catch (error) {
      res.status(500).json({
        success: false,
        message: error.message || 'Internal server error'
      });
    }
  };
};"""
        
        self.code_editor.setPlainText(template)
        self.tab_widget.setCurrentIndex(0)
        self.parse_placeholders()
        
    def create_mongodb_schema_template(self):
        """Create MongoDB schema template"""
        template = """import mongoose, { Document, Schema } from 'mongoose';

export interface {{INTERFACE_NAME}} extends Document {
  {{FIELD_NAME}}: {{FIELD_TYPE}};
  {{FIELD_NAME_2}}: {{FIELD_TYPE_2}};
  createdAt: Date;
  updatedAt: Date;
}

const {{SCHEMA_NAME}}Schema: Schema = new Schema({
  {{FIELD_NAME}}: {
    type: {{MONGOOSE_TYPE}},
    required: {{IS_REQUIRED}},
    {{FIELD_OPTIONS}}
  },
  {{FIELD_NAME_2}}: {
    type: {{MONGOOSE_TYPE_2}},
    required: {{IS_REQUIRED_2}},
    {{FIELD_OPTIONS_2}}
  }
}, {
  timestamps: true
});

// {{SCHEMA_DESCRIPTION}}
{{SCHEMA_NAME}}Schema.index({ {{INDEX_FIELD}}: 1 });

export default mongoose.model<{{INTERFACE_NAME}}>('{{MODEL_NAME}}', {{SCHEMA_NAME}}Schema);"""
        
        self.code_editor.setPlainText(template)
        self.tab_widget.setCurrentIndex(0)
        self.parse_placeholders()
        
    def create_python_class_template(self):
        """Create Python class template"""
        template = """class {{CLASS_NAME}}:
    \"\"\"{{CLASS_DESCRIPTION}}\"\"\"
    
    def __init__(self, {{INIT_PARAMS}}):
        \"\"\"Initialize {{CLASS_NAME}}
        
        Args:
            {{INIT_PARAMS}}: {{PARAM_DESCRIPTION}}
        \"\"\"
        self.{{ATTRIBUTE_NAME}} = {{ATTRIBUTE_VALUE}}
        
    def {{METHOD_NAME}}(self, {{METHOD_PARAMS}}):
        \"\"\"{{METHOD_DESCRIPTION}}
        
        Args:
            {{METHOD_PARAMS}}: {{METHOD_PARAM_DESCRIPTION}}
            
        Returns:
            {{RETURN_TYPE}}: {{RETURN_DESCRIPTION}}
        \"\"\"
        # {{METHOD_IMPLEMENTATION}}
        return {{RETURN_VALUE}}
        
    def __str__(self):
        \"\"\"String representation of {{CLASS_NAME}}\"\"\"
        return f"{{CLASS_NAME}}({{ATTRIBUTE_NAME}}={self.{{ATTRIBUTE_NAME}}})"
        
    def __repr__(self):
        \"\"\"Developer representation of {{CLASS_NAME}}\"\"\"
        return self.__str__()"""
        
        self.code_editor.setPlainText(template)
        self.tab_widget.setCurrentIndex(0)
        self.parse_placeholders()
        
    def create_python_function_template(self):
        """Create Python function template"""
        template = """def {{FUNCTION_NAME}}({{FUNCTION_PARAMS}}) -> {{RETURN_TYPE}}:
    \"\"\"{{FUNCTION_DESCRIPTION}}
    
    Args:
        {{FUNCTION_PARAMS}}: {{PARAM_DESCRIPTION}}
        
    Returns:
        {{RETURN_TYPE}}: {{RETURN_DESCRIPTION}}
        
    Raises:
        {{EXCEPTION_TYPE}}: {{EXCEPTION_DESCRIPTION}}
    \"\"\"
    try:
        # {{FUNCTION_IMPLEMENTATION}}
        result = {{COMPUTATION}}
        return result
    except Exception as e:
        raise {{EXCEPTION_TYPE}}(f"Error in {{FUNCTION_NAME}}: {str(e)}")"""
        
        self.code_editor.setPlainText(template)
        self.tab_widget.setCurrentIndex(0)
        self.parse_placeholders()
        
    def create_flask_route_template(self):
        """Create Flask route template"""
        template = """from flask import Blueprint, request, jsonify
from {{MODULE_NAME}} import {{SERVICE_NAME}}

{{BLUEPRINT_NAME}}_bp = Blueprint('{{BLUEPRINT_NAME}}', __name__)

@{{BLUEPRINT_NAME}}_bp.route('{{ROUTE_PATH}}', methods=['{{HTTP_METHOD}}'])
def {{FUNCTION_NAME}}():
    \"\"\"{{ROUTE_DESCRIPTION}}\"\"\"
    try:
        # Get request data
        {{PARAM_NAME}} = request.{{REQUEST_SOURCE}}.get('{{PARAM_KEY}}')
        
        if not {{PARAM_NAME}}:
            return jsonify({
                'success': False,
                'message': '{{PARAM_KEY}} is required'
            }), 400
        
        # Process request
        result = {{SERVICE_NAME}}.{{METHOD_NAME}}({{PARAM_NAME}})
        
        return jsonify({
            'success': True,
            'data': result,
            'message': '{{SUCCESS_MESSAGE}}'
        }), {{SUCCESS_STATUS}}
        
    except Exception as e:
        return jsonify({
            'success': False,
            'message': str(e)
        }), {{ERROR_STATUS}}"""
        
        self.code_editor.setPlainText(template)
        self.tab_widget.setCurrentIndex(0)
        self.parse_placeholders()
        
    def create_html_page_template(self):
        """Create HTML page template"""
        template = """<!DOCTYPE html>
<html lang="{{LANGUAGE}}">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{PAGE_TITLE}}</title>
    <meta name="description" content="{{PAGE_DESCRIPTION}}">
    <link rel="stylesheet" href="{{CSS_FILE}}">
</head>
<body>
    <header>
        <nav>
            <h1>{{SITE_NAME}}</h1>
            <ul>
                <li><a href="{{NAV_LINK_1}}">{{NAV_TEXT_1}}</a></li>
                <li><a href="{{NAV_LINK_2}}">{{NAV_TEXT_2}}</a></li>
            </ul>
        </nav>
    </header>
    
    <main>
        <section class="{{SECTION_CLASS}}">
            <h2>{{SECTION_TITLE}}</h2>
            <p>{{SECTION_CONTENT}}</p>
        </section>
    </main>
    
    <footer>
        <p>&copy; {{CURRENT_YEAR}} {{COMPANY_NAME}}. All rights reserved.</p>
    </footer>
    
    <script src="{{JS_FILE}}"></script>
</body>
</html>"""
        
        self.code_editor.setPlainText(template)
        self.tab_widget.setCurrentIndex(0)
        self.parse_placeholders()
        
    def create_css_component_template(self):
        """Create CSS component template"""
        template = """.{{COMPONENT_NAME}} {
  /* {{COMPONENT_DESCRIPTION}} */
  display: {{DISPLAY_TYPE}};
  {{LAYOUT_PROPERTIES}}
  
  /* Spacing */
  margin: {{MARGIN}};
  padding: {{PADDING}};
  
  /* Colors */
  background-color: {{BACKGROUND_COLOR}};
  color: {{TEXT_COLOR}};
  border: {{BORDER}};
  
  /* Typography */
  font-family: {{FONT_FAMILY}};
  font-size: {{FONT_SIZE}};
  font-weight: {{FONT_WEIGHT}};
  
  /* Effects */
  border-radius: {{BORDER_RADIUS}};
  box-shadow: {{BOX_SHADOW}};
  transition: {{TRANSITION}};
}

.{{COMPONENT_NAME}}:hover {
  {{HOVER_PROPERTIES}}
}

.{{COMPONENT_NAME}}--{{MODIFIER}} {
  {{MODIFIER_PROPERTIES}}
}

.{{COMPONENT_NAME}}__{{ELEMENT}} {
  {{ELEMENT_PROPERTIES}}
}

@media (max-width: {{BREAKPOINT}}) {
  .{{COMPONENT_NAME}} {
    {{MOBILE_PROPERTIES}}
  }
}"""
        
        self.code_editor.setPlainText(template)
        self.tab_widget.setCurrentIndex(0)
        self.parse_placeholders()


def main():
    app = QApplication(sys.argv)
    app.setStyle('Fusion')  # Use Fusion style for better appearance
    
    # Set application icon and metadata
    app.setApplicationName("Advanced Code Generator")
    app.setApplicationVersion("2.0")
    app.setOrganizationName("CodeGen Tools")
    
    window = CodeGeneratorApp()
    window.show()
    
    sys.exit(app.exec())


if __name__ == "__main__":
    main()