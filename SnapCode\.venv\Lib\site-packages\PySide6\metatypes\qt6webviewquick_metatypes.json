[{"classes": [{"className": "QQuickViewController", "lineNumber": 27, "object": true, "qualifiedClassName": "QQuickViewController", "slots": [{"access": "public", "arguments": [{"name": "window", "type": "QQuickWindow*"}], "index": 0, "name": "onWindowChanged", "returnType": "void"}, {"access": "public", "index": 1, "name": "onVisibleChanged", "returnType": "void"}, {"access": "private", "index": 2, "name": "scheduleUpdatePolish", "returnType": "void"}, {"access": "private", "index": 3, "name": "onSceneGraphInvalidated", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QQuickItem"}]}], "inputFile": "qquickviewcontroller_p.h", "outputRevision": 69}, {"classes": [{"classInfos": [{"name": "QML.Element", "value": "WebView"}, {"name": "QML.AddedInVersion", "value": "256"}, {"name": "QML.ExtraVersion", "value": "512"}], "className": "QQuickWebView", "enums": [{"isClass": false, "isFlag": false, "name": "LoadStatus", "values": ["LoadStartedStatus", "LoadStoppedStatus", "LoadSucceededStatus", "LoadFailedStatus"]}], "lineNumber": 33, "object": true, "properties": [{"constant": false, "designable": true, "final": true, "index": 0, "name": "httpUserAgent", "notify": "httpUserAgentChanged", "read": "httpUserAgent", "required": false, "revision": 270, "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "setHttpUserAgent"}, {"constant": false, "designable": true, "final": true, "index": 1, "name": "url", "notify": "url<PERSON><PERSON><PERSON>", "read": "url", "required": false, "scriptable": true, "stored": true, "type": "QUrl", "user": false, "write": "setUrl"}, {"constant": false, "designable": true, "final": true, "index": 2, "name": "loading", "notify": "loadingChanged", "read": "isLoading", "required": false, "revision": 257, "scriptable": true, "stored": true, "type": "bool", "user": false}, {"constant": false, "designable": true, "final": true, "index": 3, "name": "loadProgress", "notify": "loadProgressChanged", "read": "loadProgress", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false}, {"constant": false, "designable": true, "final": true, "index": 4, "name": "title", "notify": "titleChanged", "read": "title", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false}, {"constant": false, "designable": true, "final": true, "index": 5, "name": "canGoBack", "notify": "loadingChanged", "read": "canGoBack", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false}, {"constant": false, "designable": true, "final": true, "index": 6, "name": "canGoForward", "notify": "loadingChanged", "read": "canGoForward", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false}, {"constant": true, "designable": true, "final": true, "index": 7, "name": "settings", "read": "settings", "required": false, "revision": 1541, "scriptable": true, "stored": true, "type": "QQuickWebViewSettings*", "user": false}], "qualifiedClassName": "QQuickWebView", "signals": [{"access": "public", "index": 0, "name": "titleChanged", "returnType": "void"}, {"access": "public", "index": 1, "name": "url<PERSON><PERSON><PERSON>", "returnType": "void"}, {"access": "public", "arguments": [{"name": "loadRequest", "type": "QQuickWebViewLoadRequest*"}], "index": 2, "name": "loadingChanged", "returnType": "void", "revision": 257}, {"access": "public", "index": 3, "name": "loadProgressChanged", "returnType": "void"}, {"access": "public", "index": 4, "name": "httpUserAgentChanged", "returnType": "void", "revision": 270}, {"access": "public", "arguments": [{"name": "domain", "type": "QString"}, {"name": "name", "type": "QString"}], "index": 5, "name": "cookieAdded", "returnType": "void", "revision": 1539}, {"access": "public", "arguments": [{"name": "domain", "type": "QString"}, {"name": "name", "type": "QString"}], "index": 6, "name": "cookieRemoved", "returnType": "void", "revision": 1539}], "slots": [{"access": "public", "index": 7, "name": "goBack", "returnType": "void"}, {"access": "public", "index": 8, "name": "goForward", "returnType": "void"}, {"access": "public", "index": 9, "name": "reload", "returnType": "void"}, {"access": "public", "index": 10, "name": "stop", "returnType": "void"}, {"access": "public", "arguments": [{"name": "html", "type": "QString"}, {"name": "baseUrl", "type": "QUrl"}], "index": 11, "name": "loadHtml", "returnType": "void", "revision": 257}, {"access": "public", "arguments": [{"name": "html", "type": "QString"}], "index": 12, "isCloned": true, "name": "loadHtml", "returnType": "void", "revision": 257}, {"access": "public", "arguments": [{"name": "script", "type": "QString"}, {"name": "callback", "type": "QJSValue"}], "index": 13, "name": "runJavaScript", "returnType": "void", "revision": 257}, {"access": "public", "arguments": [{"name": "script", "type": "QString"}], "index": 14, "isCloned": true, "name": "runJavaScript", "returnType": "void", "revision": 257}, {"access": "public", "arguments": [{"name": "domain", "type": "QString"}, {"name": "name", "type": "QString"}, {"name": "value", "type": "QString"}], "index": 15, "name": "<PERSON><PERSON><PERSON><PERSON>", "returnType": "void", "revision": 1539}, {"access": "public", "arguments": [{"name": "domain", "type": "QString"}, {"name": "name", "type": "QString"}], "index": 16, "name": "deleteC<PERSON>ie", "returnType": "void", "revision": 1539}, {"access": "public", "index": 17, "name": "deleteAllCookies", "returnType": "void", "revision": 1539}, {"access": "private", "arguments": [{"name": "id", "type": "int"}, {"name": "variant", "type": "Q<PERSON><PERSON><PERSON>"}], "index": 18, "name": "onRunJavaScriptResult", "returnType": "void"}, {"access": "private", "arguments": [{"name": "focus", "type": "bool"}], "index": 19, "name": "onFocusRequest", "returnType": "void"}, {"access": "private", "arguments": [{"name": "loadRequest", "type": "QWebViewLoadRequestPrivate"}], "index": 20, "name": "onLoadingChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QQuickViewController"}, {"access": "public", "name": "QWebViewInterface"}]}], "inputFile": "qquickwebview_p.h", "outputRevision": 69}, {"classes": [{"classInfos": [{"name": "QML.Element", "value": "WebViewLoadRequest"}, {"name": "QML.AddedInVersion", "value": "257"}, {"name": "QML.ExtraVersion", "value": "512"}, {"name": "QML.Creatable", "value": "false"}, {"name": "QML.UncreatableReason", "value": ""}], "className": "QQuickWebViewLoadRequest", "lineNumber": 26, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "url", "read": "url", "required": false, "scriptable": true, "stored": true, "type": "QUrl", "user": false}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "status", "read": "status", "required": false, "scriptable": true, "stored": true, "type": "QQuickWebView::LoadStatus", "user": false}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "errorString", "read": "errorString", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false}], "qualifiedClassName": "QQuickWebViewLoadRequest", "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qquickwebviewloadrequest_p.h", "outputRevision": 69}, {"classes": [{"classInfos": [{"name": "QML.Element", "value": "WebViewSettings"}, {"name": "QML.AddedInVersion", "value": "1541"}, {"name": "QML.Creatable", "value": "false"}, {"name": "QML.UncreatableReason", "value": ""}], "className": "QQuickWebViewSettings", "lineNumber": 29, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "localStorageEnabled", "notify": "localStorageEnabledChanged", "read": "localStorageEnabled", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setLocalStorageEnabled"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "javaScriptEnabled", "notify": "javaScriptEnabledChanged", "read": "javaScriptEnabled", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setJavaScriptEnabled"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "allowFileAccess", "notify": "allowFileAccessChanged", "read": "allowFileAccess", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setAllowFileAccess"}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "localContentCanAccessFileUrls", "notify": "localContentCanAccessFileUrlsChanged", "read": "localContentCanAccessFileUrls", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setLocalContentCanAccessFileUrls"}], "qualifiedClassName": "QQuickWebViewSettings", "signals": [{"access": "public", "index": 0, "name": "localStorageEnabledChanged", "returnType": "void"}, {"access": "public", "index": 1, "name": "javaScriptEnabledChanged", "returnType": "void"}, {"access": "public", "index": 2, "name": "allowFileAccessChanged", "returnType": "void"}, {"access": "public", "index": 3, "name": "localContentCanAccessFileUrlsChanged", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "enabled", "type": "bool"}], "index": 4, "name": "setLocalStorageEnabled", "returnType": "void"}, {"access": "public", "arguments": [{"name": "enabled", "type": "bool"}], "index": 5, "name": "setJavaScriptEnabled", "returnType": "void"}, {"access": "public", "arguments": [{"name": "enabled", "type": "bool"}], "index": 6, "name": "setAllowFileAccess", "returnType": "void"}, {"access": "public", "arguments": [{"name": "enabled", "type": "bool"}], "index": 7, "name": "setLocalContentCanAccessFileUrls", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qquickwebviewsettings_p.h", "outputRevision": 69}]