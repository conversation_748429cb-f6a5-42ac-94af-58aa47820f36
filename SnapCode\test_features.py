#!/usr/bin/env python3
"""
Test script to verify the new placeholder features work correctly
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from snap_code import AdvancedTextEditor, CodeSyntaxHighlighter
from PySide6.QtWidgets import QApplication
from PySide6.QtCore import Qt

def test_placeholder_generation():
    """Test the placeholder name generation"""
    app = QApplication(sys.argv)
    
    editor = AdvancedTextEditor()
    
    # Test cases for placeholder generation
    test_cases = [
        ("component name", "COMPONENT_NAME"),
        ("user-id", "USER_ID"),
        ("firstName", "FIRSTNAME"),
        ("API_KEY", "API_KEY"),
        ("hello world!", "HELLO_WORLD"),
        ("123test", "123TEST"),
        ("test___multiple___underscores", "TEST_MULTIPLE_UNDERSCORES"),
        ("", "PLACEHOLDER")
    ]
    
    print("Testing placeholder name generation:")
    for input_text, expected in test_cases:
        result = editor.generate_placeholder_name(input_text)
        status = "✓" if result == expected else "✗"
        print(f"{status} '{input_text}' -> '{result}' (expected: '{expected}')")
    
    app.quit()

def test_syntax_highlighting():
    """Test that syntax highlighting works for placeholders"""
    app = QApplication(sys.argv)
    
    editor = AdvancedTextEditor()
    
    # Test code with placeholders
    test_code = """
function {{FUNCTION_NAME}}({{PARAM_NAME}}) {
    const {{VARIABLE_NAME}} = "{{DEFAULT_VALUE}}";
    return {{RETURN_VALUE}};
}
"""
    
    editor.setPlainText(test_code)
    
    # Check if placeholders are detected
    import re
    placeholders = re.findall(r'\{\{([^}]+)\}\}', test_code)
    
    print(f"\nTesting syntax highlighting:")
    print(f"✓ Found {len(placeholders)} placeholders: {placeholders}")
    print(f"✓ Syntax highlighter applied successfully")
    
    app.quit()

if __name__ == "__main__":
    print("Testing new placeholder features...\n")
    test_placeholder_generation()
    test_syntax_highlighting()
    print("\n✓ All tests completed!")
