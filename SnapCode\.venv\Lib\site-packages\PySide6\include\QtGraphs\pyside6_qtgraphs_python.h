// Copyright (C) 2022 The Qt Company Ltd.
// SPDX-License-Identifier: LicenseRef-Qt-Commercial OR LGPL-3.0-only OR GPL-2.0-only OR GPL-3.0-only


#ifndef SBK_QTGRAPHS_PYTHON_H
#define SBK_QTGRAPHS_PYTHON_H

#include <sbkpython.h>
#include <sbkmodule.h>
#include <sbkconverter.h>
// Module Includes
#include <pyside6_qtquick_python.h>
#include <pyside6_qtcore_python.h>
#include <pyside6_qtnetwork_python.h>
#include <pyside6_qtgui_python.h>
#include <pyside6_qtopengl_python.h>
#include <pyside6_qtqml_python.h>

// Bound library includes
#include <QtGraphs/qabstract3daxis.h>
#include <QtGraphs/qabstract3dseries.h>
#include <QtGraphs/qabstractaxis.h>
#include <QtGraphs/qabstractdataproxy.h>
#include <QtGraphs/qabstractseries.h>
#include <QtGraphs/qbardataproxy.h>
#include <QtGraphs/qbarseries.h>
#include <QtGraphs/qgraphs3dnamespace.h>
#include <QtGraphs/qgraphstheme.h>
#include <QtGraphs/qitemmodelbardataproxy.h>
#include <QtGraphs/qitemmodelsurfacedataproxy.h>
#include <QtGraphs/qpieslice.h>
#include <QtGraphs/qsurface3dseries.h>

QT_BEGIN_NAMESPACE
class Q3DScene;
class QAreaSeries;
class QBar3DSeries;
class QBarCategoryAxis;
class QBarDataItem;
class QBarModelMapper;
class QBarSet;
class QCategory3DAxis;
class QCustom3DItem;
class QCustom3DLabel;
class QCustom3DVolume;
class QDateTimeAxis;
class QGraphsLine;
struct QGraphsThemeDirtyBitField;
class QHeightMapSurfaceDataProxy;
class QItemModelScatterDataProxy;
struct QLegendData;
class QLineSeries;
class QLogValue3DAxisFormatter;
class QPieModelMapper;
class QPieSeries;
class QScatter3DSeries;
class QScatterDataItem;
class QScatterDataProxy;
class QScatterSeries;
class QSpline3DSeries;
class QSplineSeries;
class QSurfaceDataItem;
class QSurfaceDataProxy;
class QValue3DAxis;
class QValue3DAxisFormatter;
class QValueAxis;
class QXYModelMapper;
class QXYSeries;
QT_END_NAMESPACE

// Type indices
enum [[deprecated]] : int {
    SBK_Q3DSCENE_IDX                                         = 0,
    SBK_QABSTRACT3DAXIS_AXISORIENTATION_IDX                  = 4,
    SBK_QABSTRACT3DAXIS_AXISTYPE_IDX                         = 6,
    SBK_QABSTRACT3DAXIS_IDX                                  = 2,
    SBK_QABSTRACT3DSERIES_SERIESTYPE_IDX                     = 12,
    SBK_QABSTRACT3DSERIES_MESH_IDX                           = 10,
    SBK_QABSTRACT3DSERIES_IDX                                = 8,
    SBK_QABSTRACTAXIS_AXISTYPE_IDX                           = 16,
    SBK_QABSTRACTAXIS_IDX                                    = 14,
    SBK_QABSTRACTDATAPROXY_DATATYPE_IDX                      = 20,
    SBK_QABSTRACTDATAPROXY_IDX                               = 18,
    SBK_QABSTRACTSERIES_SERIESTYPE_IDX                       = 24,
    SBK_QABSTRACTSERIES_IDX                                  = 22,
    SBK_QAREASERIES_IDX                                      = 26,
    SBK_QBAR3DSERIES_IDX                                     = 28,
    SBK_QBARCATEGORYAXIS_IDX                                 = 30,
    SBK_QBARDATAITEM_IDX                                     = 32,
    SBK_QBARDATAPROXY_REMOVELABELS_IDX                       = 36,
    SBK_QBARDATAPROXY_IDX                                    = 34,
    SBK_QBARMODELMAPPER_IDX                                  = 38,
    SBK_QBARSERIES_LABELSPOSITION_IDX                        = 44,
    SBK_QBARSERIES_BARSTYPE_IDX                              = 42,
    SBK_QBARSERIES_IDX                                       = 40,
    SBK_QBARSET_IDX                                          = 46,
    SBK_QCATEGORY3DAXIS_IDX                                  = 48,
    SBK_QCUSTOM3DITEM_IDX                                    = 50,
    SBK_QCUSTOM3DLABEL_IDX                                   = 52,
    SBK_QCUSTOM3DVOLUME_IDX                                  = 54,
    SBK_QDATETIMEAXIS_IDX                                    = 56,
    SBK_QGRAPHSLINE_IDX                                      = 64,
    SBK_QGRAPHSTHEME_THEME_IDX                               = 74,
    SBK_QGRAPHSTHEME_COLORSTYLE_IDX                          = 70,
    SBK_QGRAPHSTHEME_FORCETHEME_IDX                          = 72,
    SBK_QGRAPHSTHEME_COLORSCHEME_IDX                         = 68,
    SBK_QGRAPHSTHEME_IDX                                     = 66,
    SBK_QGRAPHSTHEMEDIRTYBITFIELD_IDX                        = 76,
    SBK_QHEIGHTMAPSURFACEDATAPROXY_IDX                       = 78,
    SBK_QITEMMODELBARDATAPROXY_MULTIMATCHBEHAVIOR_IDX        = 82,
    SBK_QITEMMODELBARDATAPROXY_IDX                           = 80,
    SBK_QITEMMODELSCATTERDATAPROXY_IDX                       = 84,
    SBK_QITEMMODELSURFACEDATAPROXY_MULTIMATCHBEHAVIOR_IDX    = 88,
    SBK_QITEMMODELSURFACEDATAPROXY_IDX                       = 86,
    SBK_QLEGENDDATA_IDX                                      = 90,
    SBK_QLINESERIES_IDX                                      = 92,
    SBK_QLOGVALUE3DAXISFORMATTER_IDX                         = 94,
    SBK_QPIEMODELMAPPER_IDX                                  = 96,
    SBK_QPIESERIES_IDX                                       = 98,
    SBK_QPIESLICE_LABELPOSITION_IDX                          = 102,
    SBK_QPIESLICE_IDX                                        = 100,
    SBK_QSCATTER3DSERIES_IDX                                 = 104,
    SBK_QSCATTERDATAITEM_IDX                                 = 106,
    SBK_QSCATTERDATAPROXY_IDX                                = 108,
    SBK_QSCATTERSERIES_IDX                                   = 110,
    SBK_QSPLINE3DSERIES_IDX                                  = 112,
    SBK_QSPLINESERIES_IDX                                    = 114,
    SBK_QSURFACE3DSERIES_DRAWFLAG_IDX                        = 118,
    SBK_QFLAGS_QSURFACE3DSERIES_DRAWFLAG_IDX                 = 58,
    SBK_QSURFACE3DSERIES_SHADING_IDX                         = 120,
    SBK_QSURFACE3DSERIES_IDX                                 = 116,
    SBK_QSURFACEDATAITEM_IDX                                 = 122,
    SBK_QSURFACEDATAPROXY_IDX                                = 124,
    SBK_QVALUE3DAXIS_IDX                                     = 126,
    SBK_QVALUE3DAXISFORMATTER_IDX                            = 128,
    SBK_QVALUEAXIS_IDX                                       = 130,
    SBK_QXYMODELMAPPER_IDX                                   = 132,
    SBK_QXYSERIES_IDX                                        = 134,
    SBK_QTGRAPHS3D_SELECTIONFLAG_IDX                         = 148,
    SBK_QFLAGS_QTGRAPHS3D_SELECTIONFLAG_IDX                  = 62,
    SBK_QTGRAPHS3D_SHADOWQUALITY_IDX                         = 150,
    SBK_QTGRAPHS3D_ELEMENTTYPE_IDX                           = 140,
    SBK_QTGRAPHS3D_OPTIMIZATIONHINT_IDX                      = 144,
    SBK_QFLAGS_QTGRAPHS3D_OPTIMIZATIONHINT_IDX               = 60,
    SBK_QTGRAPHS3D_RENDERINGMODE_IDX                         = 146,
    SBK_QTGRAPHS3D_CAMERAPRESET_IDX                          = 138,
    SBK_QTGRAPHS3D_GRIDLINETYPE_IDX                          = 142,
    SBK_QTGRAPHS3D_TRANSPARENCYTECHNIQUE_IDX                 = 152,
    SBK_QTGRAPHSQTGRAPHS3D_IDX                               = 136,
    SBK_QTGRAPHS_IDX_COUNT                                   = 154,
};

// Type indices
enum : int {
    SBK_Q3DScene_IDX                                         = 0,
    SBK_QAbstract3DAxis_AxisOrientation_IDX                  = 2,
    SBK_QAbstract3DAxis_AxisType_IDX                         = 3,
    SBK_QAbstract3DAxis_IDX                                  = 1,
    SBK_QAbstract3DSeries_SeriesType_IDX                     = 6,
    SBK_QAbstract3DSeries_Mesh_IDX                           = 5,
    SBK_QAbstract3DSeries_IDX                                = 4,
    SBK_QAbstractAxis_AxisType_IDX                           = 8,
    SBK_QAbstractAxis_IDX                                    = 7,
    SBK_QAbstractDataProxy_DataType_IDX                      = 10,
    SBK_QAbstractDataProxy_IDX                               = 9,
    SBK_QAbstractSeries_SeriesType_IDX                       = 12,
    SBK_QAbstractSeries_IDX                                  = 11,
    SBK_QAreaSeries_IDX                                      = 13,
    SBK_QBar3DSeries_IDX                                     = 14,
    SBK_QBarCategoryAxis_IDX                                 = 15,
    SBK_QBarDataItem_IDX                                     = 16,
    SBK_QBarDataProxy_RemoveLabels_IDX                       = 18,
    SBK_QBarDataProxy_IDX                                    = 17,
    SBK_QBarModelMapper_IDX                                  = 19,
    SBK_QBarSeries_LabelsPosition_IDX                        = 22,
    SBK_QBarSeries_BarsType_IDX                              = 21,
    SBK_QBarSeries_IDX                                       = 20,
    SBK_QBarSet_IDX                                          = 23,
    SBK_QCategory3DAxis_IDX                                  = 24,
    SBK_QCustom3DItem_IDX                                    = 25,
    SBK_QCustom3DLabel_IDX                                   = 26,
    SBK_QCustom3DVolume_IDX                                  = 27,
    SBK_QDateTimeAxis_IDX                                    = 28,
    SBK_QGraphsLine_IDX                                      = 32,
    SBK_QGraphsTheme_Theme_IDX                               = 37,
    SBK_QGraphsTheme_ColorStyle_IDX                          = 35,
    SBK_QGraphsTheme_ForceTheme_IDX                          = 36,
    SBK_QGraphsTheme_ColorScheme_IDX                         = 34,
    SBK_QGraphsTheme_IDX                                     = 33,
    SBK_QGraphsThemeDirtyBitField_IDX                        = 38,
    SBK_QHeightMapSurfaceDataProxy_IDX                       = 39,
    SBK_QItemModelBarDataProxy_MultiMatchBehavior_IDX        = 41,
    SBK_QItemModelBarDataProxy_IDX                           = 40,
    SBK_QItemModelScatterDataProxy_IDX                       = 42,
    SBK_QItemModelSurfaceDataProxy_MultiMatchBehavior_IDX    = 44,
    SBK_QItemModelSurfaceDataProxy_IDX                       = 43,
    SBK_QLegendData_IDX                                      = 45,
    SBK_QLineSeries_IDX                                      = 46,
    SBK_QLogValue3DAxisFormatter_IDX                         = 47,
    SBK_QPieModelMapper_IDX                                  = 48,
    SBK_QPieSeries_IDX                                       = 49,
    SBK_QPieSlice_LabelPosition_IDX                          = 51,
    SBK_QPieSlice_IDX                                        = 50,
    SBK_QScatter3DSeries_IDX                                 = 52,
    SBK_QScatterDataItem_IDX                                 = 53,
    SBK_QScatterDataProxy_IDX                                = 54,
    SBK_QScatterSeries_IDX                                   = 55,
    SBK_QSpline3DSeries_IDX                                  = 56,
    SBK_QSplineSeries_IDX                                    = 57,
    SBK_QSurface3DSeries_DrawFlag_IDX                        = 59,
    SBK_QFlags_QSurface3DSeries_DrawFlag_IDX                 = 29,
    SBK_QSurface3DSeries_Shading_IDX                         = 60,
    SBK_QSurface3DSeries_IDX                                 = 58,
    SBK_QSurfaceDataItem_IDX                                 = 61,
    SBK_QSurfaceDataProxy_IDX                                = 62,
    SBK_QValue3DAxis_IDX                                     = 63,
    SBK_QValue3DAxisFormatter_IDX                            = 64,
    SBK_QValueAxis_IDX                                       = 65,
    SBK_QXYModelMapper_IDX                                   = 66,
    SBK_QXYSeries_IDX                                        = 67,
    SBK_QtGraphs3D_SelectionFlag_IDX                         = 74,
    SBK_QFlags_QtGraphs3D_SelectionFlag_IDX                  = 31,
    SBK_QtGraphs3D_ShadowQuality_IDX                         = 75,
    SBK_QtGraphs3D_ElementType_IDX                           = 70,
    SBK_QtGraphs3D_OptimizationHint_IDX                      = 72,
    SBK_QFlags_QtGraphs3D_OptimizationHint_IDX               = 30,
    SBK_QtGraphs3D_RenderingMode_IDX                         = 73,
    SBK_QtGraphs3D_CameraPreset_IDX                          = 69,
    SBK_QtGraphs3D_GridLineType_IDX                          = 71,
    SBK_QtGraphs3D_TransparencyTechnique_IDX                 = 76,
    SBK_QtGraphsQtGraphs3D_IDX                               = 68,
    SBK_QtGraphs_IDX_COUNT                                   = 77,
};

// This variable stores all Python types exported by this module.
extern Shiboken::Module::TypeInitStruct *SbkPySide6_QtGraphsTypeStructs;

// This variable stores all Python types exported by this module in a backwards compatible way with identical indexing.
[[deprecated]] extern PyTypeObject **SbkPySide6_QtGraphsTypes;

// This variable stores the Python module object exported by this module.
extern PyObject *SbkPySide6_QtGraphsModuleObject;

// This variable stores all type converters exported by this module.
extern SbkConverter **SbkPySide6_QtGraphsTypeConverters;

// Converter indices
enum [[deprecated]] : int {
    SBK_QTGRAPHS_QLIST_INT_IDX                               = 0, // QList<int>
    SBK_QTGRAPHS_QLIST_FLOAT_IDX                             = 2, // QList<float>
    SBK_QTGRAPHS_QLIST_QCOLOR_IDX                            = 4, // QList<QColor>
    SBK_QTGRAPHS_QLIST_QLINEARGRADIENT_IDX                   = 6, // QList<QLinearGradient>
    SBK_QTGRAPHS_QLIST_UCHAR_IDX                             = 8, // QList<uchar>
    SBK_QTGRAPHS_QLIST_UNSIGNEDINT_IDX                       = 10, // QList<unsigned int>
    SBK_QTGRAPHS_QLIST_QIMAGEPTR_IDX                         = 12, // QList<QImage*>
    SBK_QTGRAPHS_QLIST_QREAL_IDX                             = 14, // QList<qreal>
    SBK_QTGRAPHS_QLIST_QSIZETYPE_IDX                         = 16, // QList<qsizetype>
    SBK_QTGRAPHS_QLIST_QVARIANT_IDX                          = 18, // QList<QVariant>
    SBK_QTGRAPHS_QLIST_QLEGENDDATA_IDX                       = 20, // QList<QLegendData>
    SBK_QTGRAPHS_QLIST_QPOINTF_IDX                           = 22, // QList<QPointF>
    SBK_QTGRAPHS_QLIST_QPIESLICEPTR_IDX                      = 24, // QList<QPieSlice*>
    SBK_QTGRAPHS_QLIST_QBARSETPTR_IDX                        = 26, // QList<QBarSet*>
    SBK_QTGRAPHS_QLIST_QSURFACEDATAITEM_IDX                  = 28, // QList<QSurfaceDataItem>
    SBK_QTGRAPHS_QLIST_QLIST_QSURFACEDATAITEM_IDX            = 30, // QList<QList<QSurfaceDataItem>>
    SBK_QTGRAPHS_QLIST_QSCATTERDATAITEM_IDX                  = 32, // QList<QScatterDataItem>
    SBK_QTGRAPHS_QLIST_QBARDATAITEM_IDX                      = 34, // QList<QBarDataItem>
    SBK_QTGRAPHS_QLIST_QLIST_QBARDATAITEM_IDX                = 36, // QList<QList<QBarDataItem>>
    SBK_QTGRAPHS_QLIST_QSTRING_IDX                           = 38, // QList<QString>
    SBK_QTGRAPHS_QMAP_QSTRING_QVARIANT_IDX                   = 40, // QMap<QString,QVariant>
    SBK_QTGRAPHS_CONVERTERS_IDX_COUNT                        = 42,
};

// Converter indices
enum : int {
    SBK_QtGraphs_QList_int_IDX                               = 0, // QList<int>
    SBK_QtGraphs_QList_float_IDX                             = 1, // QList<float>
    SBK_QtGraphs_QList_QColor_IDX                            = 2, // QList<QColor>
    SBK_QtGraphs_QList_QLinearGradient_IDX                   = 3, // QList<QLinearGradient>
    SBK_QtGraphs_QList_uchar_IDX                             = 4, // QList<uchar>
    SBK_QtGraphs_QList_unsignedint_IDX                       = 5, // QList<unsigned int>
    SBK_QtGraphs_QList_QImagePTR_IDX                         = 6, // QList<QImage*>
    SBK_QtGraphs_QList_qreal_IDX                             = 7, // QList<qreal>
    SBK_QtGraphs_QList_qsizetype_IDX                         = 8, // QList<qsizetype>
    SBK_QtGraphs_QList_QVariant_IDX                          = 9, // QList<QVariant>
    SBK_QtGraphs_QList_QLegendData_IDX                       = 10, // QList<QLegendData>
    SBK_QtGraphs_QList_QPointF_IDX                           = 11, // QList<QPointF>
    SBK_QtGraphs_QList_QPieSlicePTR_IDX                      = 12, // QList<QPieSlice*>
    SBK_QtGraphs_QList_QBarSetPTR_IDX                        = 13, // QList<QBarSet*>
    SBK_QtGraphs_QList_QSurfaceDataItem_IDX                  = 14, // QList<QSurfaceDataItem>
    SBK_QtGraphs_QList_QList_QSurfaceDataItem_IDX            = 15, // QList<QList<QSurfaceDataItem>>
    SBK_QtGraphs_QList_QScatterDataItem_IDX                  = 16, // QList<QScatterDataItem>
    SBK_QtGraphs_QList_QBarDataItem_IDX                      = 17, // QList<QBarDataItem>
    SBK_QtGraphs_QList_QList_QBarDataItem_IDX                = 18, // QList<QList<QBarDataItem>>
    SBK_QtGraphs_QList_QString_IDX                           = 19, // QList<QString>
    SBK_QtGraphs_QMap_QString_QVariant_IDX                   = 20, // QMap<QString,QVariant>
    SBK_QtGraphs_CONVERTERS_IDX_COUNT                        = 21,
};
// Macros for type check

QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
namespace Shiboken
{

// PyType functions, to get the PyObjectType for a type T
template<> inline PyTypeObject *SbkType< ::Q3DScene >() { return Shiboken::Module::get(SbkPySide6_QtGraphsTypeStructs[SBK_Q3DScene_IDX]); }
template<> inline PyTypeObject *SbkType< ::QAbstract3DAxis::AxisOrientation >() { return Shiboken::Module::get(SbkPySide6_QtGraphsTypeStructs[SBK_QAbstract3DAxis_AxisOrientation_IDX]); }
template<> inline PyTypeObject *SbkType< ::QAbstract3DAxis::AxisType >() { return Shiboken::Module::get(SbkPySide6_QtGraphsTypeStructs[SBK_QAbstract3DAxis_AxisType_IDX]); }
template<> inline PyTypeObject *SbkType< ::QAbstract3DAxis >() { return Shiboken::Module::get(SbkPySide6_QtGraphsTypeStructs[SBK_QAbstract3DAxis_IDX]); }
template<> inline PyTypeObject *SbkType< ::QAbstract3DSeries::SeriesType >() { return Shiboken::Module::get(SbkPySide6_QtGraphsTypeStructs[SBK_QAbstract3DSeries_SeriesType_IDX]); }
template<> inline PyTypeObject *SbkType< ::QAbstract3DSeries::Mesh >() { return Shiboken::Module::get(SbkPySide6_QtGraphsTypeStructs[SBK_QAbstract3DSeries_Mesh_IDX]); }
template<> inline PyTypeObject *SbkType< ::QAbstract3DSeries >() { return Shiboken::Module::get(SbkPySide6_QtGraphsTypeStructs[SBK_QAbstract3DSeries_IDX]); }
template<> inline PyTypeObject *SbkType< ::QAbstractAxis::AxisType >() { return Shiboken::Module::get(SbkPySide6_QtGraphsTypeStructs[SBK_QAbstractAxis_AxisType_IDX]); }
template<> inline PyTypeObject *SbkType< ::QAbstractAxis >() { return Shiboken::Module::get(SbkPySide6_QtGraphsTypeStructs[SBK_QAbstractAxis_IDX]); }
template<> inline PyTypeObject *SbkType< ::QAbstractDataProxy::DataType >() { return Shiboken::Module::get(SbkPySide6_QtGraphsTypeStructs[SBK_QAbstractDataProxy_DataType_IDX]); }
template<> inline PyTypeObject *SbkType< ::QAbstractDataProxy >() { return Shiboken::Module::get(SbkPySide6_QtGraphsTypeStructs[SBK_QAbstractDataProxy_IDX]); }
template<> inline PyTypeObject *SbkType< ::QAbstractSeries::SeriesType >() { return Shiboken::Module::get(SbkPySide6_QtGraphsTypeStructs[SBK_QAbstractSeries_SeriesType_IDX]); }
template<> inline PyTypeObject *SbkType< ::QAbstractSeries >() { return Shiboken::Module::get(SbkPySide6_QtGraphsTypeStructs[SBK_QAbstractSeries_IDX]); }
template<> inline PyTypeObject *SbkType< ::QAreaSeries >() { return Shiboken::Module::get(SbkPySide6_QtGraphsTypeStructs[SBK_QAreaSeries_IDX]); }
template<> inline PyTypeObject *SbkType< ::QBar3DSeries >() { return Shiboken::Module::get(SbkPySide6_QtGraphsTypeStructs[SBK_QBar3DSeries_IDX]); }
template<> inline PyTypeObject *SbkType< ::QBarCategoryAxis >() { return Shiboken::Module::get(SbkPySide6_QtGraphsTypeStructs[SBK_QBarCategoryAxis_IDX]); }
template<> inline PyTypeObject *SbkType< ::QBarDataItem >() { return Shiboken::Module::get(SbkPySide6_QtGraphsTypeStructs[SBK_QBarDataItem_IDX]); }
template<> inline PyTypeObject *SbkType< ::QBarDataProxy::RemoveLabels >() { return Shiboken::Module::get(SbkPySide6_QtGraphsTypeStructs[SBK_QBarDataProxy_RemoveLabels_IDX]); }
template<> inline PyTypeObject *SbkType< ::QBarDataProxy >() { return Shiboken::Module::get(SbkPySide6_QtGraphsTypeStructs[SBK_QBarDataProxy_IDX]); }
template<> inline PyTypeObject *SbkType< ::QBarModelMapper >() { return Shiboken::Module::get(SbkPySide6_QtGraphsTypeStructs[SBK_QBarModelMapper_IDX]); }
template<> inline PyTypeObject *SbkType< ::QBarSeries::LabelsPosition >() { return Shiboken::Module::get(SbkPySide6_QtGraphsTypeStructs[SBK_QBarSeries_LabelsPosition_IDX]); }
template<> inline PyTypeObject *SbkType< ::QBarSeries::BarsType >() { return Shiboken::Module::get(SbkPySide6_QtGraphsTypeStructs[SBK_QBarSeries_BarsType_IDX]); }
template<> inline PyTypeObject *SbkType< ::QBarSeries >() { return Shiboken::Module::get(SbkPySide6_QtGraphsTypeStructs[SBK_QBarSeries_IDX]); }
template<> inline PyTypeObject *SbkType< ::QBarSet >() { return Shiboken::Module::get(SbkPySide6_QtGraphsTypeStructs[SBK_QBarSet_IDX]); }
template<> inline PyTypeObject *SbkType< ::QCategory3DAxis >() { return Shiboken::Module::get(SbkPySide6_QtGraphsTypeStructs[SBK_QCategory3DAxis_IDX]); }
template<> inline PyTypeObject *SbkType< ::QCustom3DItem >() { return Shiboken::Module::get(SbkPySide6_QtGraphsTypeStructs[SBK_QCustom3DItem_IDX]); }
template<> inline PyTypeObject *SbkType< ::QCustom3DLabel >() { return Shiboken::Module::get(SbkPySide6_QtGraphsTypeStructs[SBK_QCustom3DLabel_IDX]); }
template<> inline PyTypeObject *SbkType< ::QCustom3DVolume >() { return Shiboken::Module::get(SbkPySide6_QtGraphsTypeStructs[SBK_QCustom3DVolume_IDX]); }
template<> inline PyTypeObject *SbkType< ::QDateTimeAxis >() { return Shiboken::Module::get(SbkPySide6_QtGraphsTypeStructs[SBK_QDateTimeAxis_IDX]); }
template<> inline PyTypeObject *SbkType< ::QGraphsLine >() { return Shiboken::Module::get(SbkPySide6_QtGraphsTypeStructs[SBK_QGraphsLine_IDX]); }
template<> inline PyTypeObject *SbkType< ::QGraphsTheme::Theme >() { return Shiboken::Module::get(SbkPySide6_QtGraphsTypeStructs[SBK_QGraphsTheme_Theme_IDX]); }
template<> inline PyTypeObject *SbkType< ::QGraphsTheme::ColorStyle >() { return Shiboken::Module::get(SbkPySide6_QtGraphsTypeStructs[SBK_QGraphsTheme_ColorStyle_IDX]); }
template<> inline PyTypeObject *SbkType< ::QGraphsTheme::ForceTheme >() { return Shiboken::Module::get(SbkPySide6_QtGraphsTypeStructs[SBK_QGraphsTheme_ForceTheme_IDX]); }
template<> inline PyTypeObject *SbkType< ::QGraphsTheme::ColorScheme >() { return Shiboken::Module::get(SbkPySide6_QtGraphsTypeStructs[SBK_QGraphsTheme_ColorScheme_IDX]); }
template<> inline PyTypeObject *SbkType< ::QGraphsTheme >() { return Shiboken::Module::get(SbkPySide6_QtGraphsTypeStructs[SBK_QGraphsTheme_IDX]); }
template<> inline PyTypeObject *SbkType< ::QGraphsThemeDirtyBitField >() { return Shiboken::Module::get(SbkPySide6_QtGraphsTypeStructs[SBK_QGraphsThemeDirtyBitField_IDX]); }
template<> inline PyTypeObject *SbkType< ::QHeightMapSurfaceDataProxy >() { return Shiboken::Module::get(SbkPySide6_QtGraphsTypeStructs[SBK_QHeightMapSurfaceDataProxy_IDX]); }
template<> inline PyTypeObject *SbkType< ::QItemModelBarDataProxy::MultiMatchBehavior >() { return Shiboken::Module::get(SbkPySide6_QtGraphsTypeStructs[SBK_QItemModelBarDataProxy_MultiMatchBehavior_IDX]); }
template<> inline PyTypeObject *SbkType< ::QItemModelBarDataProxy >() { return Shiboken::Module::get(SbkPySide6_QtGraphsTypeStructs[SBK_QItemModelBarDataProxy_IDX]); }
template<> inline PyTypeObject *SbkType< ::QItemModelScatterDataProxy >() { return Shiboken::Module::get(SbkPySide6_QtGraphsTypeStructs[SBK_QItemModelScatterDataProxy_IDX]); }
template<> inline PyTypeObject *SbkType< ::QItemModelSurfaceDataProxy::MultiMatchBehavior >() { return Shiboken::Module::get(SbkPySide6_QtGraphsTypeStructs[SBK_QItemModelSurfaceDataProxy_MultiMatchBehavior_IDX]); }
template<> inline PyTypeObject *SbkType< ::QItemModelSurfaceDataProxy >() { return Shiboken::Module::get(SbkPySide6_QtGraphsTypeStructs[SBK_QItemModelSurfaceDataProxy_IDX]); }
template<> inline PyTypeObject *SbkType< ::QLegendData >() { return Shiboken::Module::get(SbkPySide6_QtGraphsTypeStructs[SBK_QLegendData_IDX]); }
template<> inline PyTypeObject *SbkType< ::QLineSeries >() { return Shiboken::Module::get(SbkPySide6_QtGraphsTypeStructs[SBK_QLineSeries_IDX]); }
template<> inline PyTypeObject *SbkType< ::QLogValue3DAxisFormatter >() { return Shiboken::Module::get(SbkPySide6_QtGraphsTypeStructs[SBK_QLogValue3DAxisFormatter_IDX]); }
template<> inline PyTypeObject *SbkType< ::QPieModelMapper >() { return Shiboken::Module::get(SbkPySide6_QtGraphsTypeStructs[SBK_QPieModelMapper_IDX]); }
template<> inline PyTypeObject *SbkType< ::QPieSeries >() { return Shiboken::Module::get(SbkPySide6_QtGraphsTypeStructs[SBK_QPieSeries_IDX]); }
template<> inline PyTypeObject *SbkType< ::QPieSlice::LabelPosition >() { return Shiboken::Module::get(SbkPySide6_QtGraphsTypeStructs[SBK_QPieSlice_LabelPosition_IDX]); }
template<> inline PyTypeObject *SbkType< ::QPieSlice >() { return Shiboken::Module::get(SbkPySide6_QtGraphsTypeStructs[SBK_QPieSlice_IDX]); }
template<> inline PyTypeObject *SbkType< ::QScatter3DSeries >() { return Shiboken::Module::get(SbkPySide6_QtGraphsTypeStructs[SBK_QScatter3DSeries_IDX]); }
template<> inline PyTypeObject *SbkType< ::QScatterDataItem >() { return Shiboken::Module::get(SbkPySide6_QtGraphsTypeStructs[SBK_QScatterDataItem_IDX]); }
template<> inline PyTypeObject *SbkType< ::QScatterDataProxy >() { return Shiboken::Module::get(SbkPySide6_QtGraphsTypeStructs[SBK_QScatterDataProxy_IDX]); }
template<> inline PyTypeObject *SbkType< ::QScatterSeries >() { return Shiboken::Module::get(SbkPySide6_QtGraphsTypeStructs[SBK_QScatterSeries_IDX]); }
template<> inline PyTypeObject *SbkType< ::QSpline3DSeries >() { return Shiboken::Module::get(SbkPySide6_QtGraphsTypeStructs[SBK_QSpline3DSeries_IDX]); }
template<> inline PyTypeObject *SbkType< ::QSplineSeries >() { return Shiboken::Module::get(SbkPySide6_QtGraphsTypeStructs[SBK_QSplineSeries_IDX]); }
template<> inline PyTypeObject *SbkType< ::QSurface3DSeries::DrawFlag >() { return Shiboken::Module::get(SbkPySide6_QtGraphsTypeStructs[SBK_QSurface3DSeries_DrawFlag_IDX]); }
template<> inline PyTypeObject *SbkType< ::QFlags<QSurface3DSeries::DrawFlag> >() { return Shiboken::Module::get(SbkPySide6_QtGraphsTypeStructs[SBK_QFlags_QSurface3DSeries_DrawFlag_IDX]); }
template<> inline PyTypeObject *SbkType< ::QSurface3DSeries::Shading >() { return Shiboken::Module::get(SbkPySide6_QtGraphsTypeStructs[SBK_QSurface3DSeries_Shading_IDX]); }
template<> inline PyTypeObject *SbkType< ::QSurface3DSeries >() { return Shiboken::Module::get(SbkPySide6_QtGraphsTypeStructs[SBK_QSurface3DSeries_IDX]); }
template<> inline PyTypeObject *SbkType< ::QSurfaceDataItem >() { return Shiboken::Module::get(SbkPySide6_QtGraphsTypeStructs[SBK_QSurfaceDataItem_IDX]); }
template<> inline PyTypeObject *SbkType< ::QSurfaceDataProxy >() { return Shiboken::Module::get(SbkPySide6_QtGraphsTypeStructs[SBK_QSurfaceDataProxy_IDX]); }
template<> inline PyTypeObject *SbkType< ::QValue3DAxis >() { return Shiboken::Module::get(SbkPySide6_QtGraphsTypeStructs[SBK_QValue3DAxis_IDX]); }
template<> inline PyTypeObject *SbkType< ::QValue3DAxisFormatter >() { return Shiboken::Module::get(SbkPySide6_QtGraphsTypeStructs[SBK_QValue3DAxisFormatter_IDX]); }
template<> inline PyTypeObject *SbkType< ::QValueAxis >() { return Shiboken::Module::get(SbkPySide6_QtGraphsTypeStructs[SBK_QValueAxis_IDX]); }
template<> inline PyTypeObject *SbkType< ::QXYModelMapper >() { return Shiboken::Module::get(SbkPySide6_QtGraphsTypeStructs[SBK_QXYModelMapper_IDX]); }
template<> inline PyTypeObject *SbkType< ::QXYSeries >() { return Shiboken::Module::get(SbkPySide6_QtGraphsTypeStructs[SBK_QXYSeries_IDX]); }
template<> inline PyTypeObject *SbkType< ::QtGraphs3D::SelectionFlag >() { return Shiboken::Module::get(SbkPySide6_QtGraphsTypeStructs[SBK_QtGraphs3D_SelectionFlag_IDX]); }
template<> inline PyTypeObject *SbkType< ::QFlags<QtGraphs3D::SelectionFlag> >() { return Shiboken::Module::get(SbkPySide6_QtGraphsTypeStructs[SBK_QFlags_QtGraphs3D_SelectionFlag_IDX]); }
template<> inline PyTypeObject *SbkType< ::QtGraphs3D::ShadowQuality >() { return Shiboken::Module::get(SbkPySide6_QtGraphsTypeStructs[SBK_QtGraphs3D_ShadowQuality_IDX]); }
template<> inline PyTypeObject *SbkType< ::QtGraphs3D::ElementType >() { return Shiboken::Module::get(SbkPySide6_QtGraphsTypeStructs[SBK_QtGraphs3D_ElementType_IDX]); }
template<> inline PyTypeObject *SbkType< ::QtGraphs3D::OptimizationHint >() { return Shiboken::Module::get(SbkPySide6_QtGraphsTypeStructs[SBK_QtGraphs3D_OptimizationHint_IDX]); }
template<> inline PyTypeObject *SbkType< ::QFlags<QtGraphs3D::OptimizationHint> >() { return Shiboken::Module::get(SbkPySide6_QtGraphsTypeStructs[SBK_QFlags_QtGraphs3D_OptimizationHint_IDX]); }
template<> inline PyTypeObject *SbkType< ::QtGraphs3D::RenderingMode >() { return Shiboken::Module::get(SbkPySide6_QtGraphsTypeStructs[SBK_QtGraphs3D_RenderingMode_IDX]); }
template<> inline PyTypeObject *SbkType< ::QtGraphs3D::CameraPreset >() { return Shiboken::Module::get(SbkPySide6_QtGraphsTypeStructs[SBK_QtGraphs3D_CameraPreset_IDX]); }
template<> inline PyTypeObject *SbkType< ::QtGraphs3D::GridLineType >() { return Shiboken::Module::get(SbkPySide6_QtGraphsTypeStructs[SBK_QtGraphs3D_GridLineType_IDX]); }
template<> inline PyTypeObject *SbkType< ::QtGraphs3D::TransparencyTechnique >() { return Shiboken::Module::get(SbkPySide6_QtGraphsTypeStructs[SBK_QtGraphs3D_TransparencyTechnique_IDX]); }

} // namespace Shiboken

QT_WARNING_POP
#endif // SBK_QTGRAPHS_PYTHON_H

