<?xml version="1.0" encoding="UTF-8"?>
<!--
// Copyright (C) 2022 The Qt Company Ltd.
// SPDX-License-Identifier: LicenseRef-Qt-Commercial OR LGPL-3.0-only OR GPL-2.0-only OR GPL-3.0-only
-->
<typesystem package="PySide6.QtNfc"
            namespace-begin="QT_BEGIN_NAMESPACE" namespace-end="QT_END_NAMESPACE">
    <load-typesystem name="typesystem_core.xml" generate="no"/>
    <value-type name="QNdefFilter">
        <value-type name="Record"/>
    </value-type>
    <value-type name="QNdefMessage"/>
    <value-type name="QNdefNfcIconRecord"/>
    <value-type name="QNdefNfcSmartPosterRecord">
        <enum-type name="Action"/>
    </value-type>
    <value-type name="QNdefNfcTextRecord">
        <enum-type name="Encoding"/>
    </value-type>
    <value-type name="QNdefNfcUriRecord"/>
    <value-type name="QNdefRecord">
        <enum-type name="TypeNameFormat"/>
    </value-type>
    <object-type name="QNearFieldManager">
        <enum-type name="AdapterState"/>
    </object-type>
    <object-type name="QNearFieldTarget">
        <enum-type name="AccessMethod" flags="AccessMethods"/>
        <enum-type name="Error"/>
        <enum-type name="Type"/>
        <value-type name="RequestId"/>
    </object-type>
    <!-- QtNetwork is pulled in via QtNfcDepends. -->
    <suppress-warning text="^Scoped enum 'Q(Ocsp)|(Dtls).*' does not have a type entry.*$"/>
</typesystem>
