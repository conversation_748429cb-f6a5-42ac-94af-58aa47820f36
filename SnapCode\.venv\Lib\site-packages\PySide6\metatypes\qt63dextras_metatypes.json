[{"classes": [{"className": "DistanceFieldTextRenderer", "lineNumber": 33, "object": true, "qualifiedClassName": "Qt3DExtras::DistanceFieldTextRenderer", "superClasses": [{"access": "public", "name": "Qt3DCore::QEntity"}]}], "inputFile": "distancefieldtextrenderer_p.h", "outputRevision": 69}, {"classes": [{"className": "QAbstractCameraController", "lineNumber": 25, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "camera", "notify": "cameraChanged", "read": "camera", "required": false, "scriptable": true, "stored": true, "type": "Qt3DRender::QCamera*", "user": false, "write": "setCamera"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "linearSpeed", "notify": "linearSpeedChanged", "read": "linearSpeed", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setLinearSpeed"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "lookSpeed", "notify": "lookSpeedChanged", "read": "lookSpeed", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setLookSpeed"}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "acceleration", "notify": "accelerationChanged", "read": "acceleration", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setAcceleration"}, {"constant": false, "designable": true, "final": false, "index": 4, "name": "deceleration", "notify": "decelerationChanged", "read": "deceleration", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setDeceleration"}], "qualifiedClassName": "Qt3DExtras::QAbstractCameraController", "signals": [{"access": "public", "index": 0, "name": "cameraChanged", "returnType": "void"}, {"access": "public", "index": 1, "name": "linearSpeedChanged", "returnType": "void"}, {"access": "public", "index": 2, "name": "lookSpeedChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "acceleration", "type": "float"}], "index": 3, "name": "accelerationChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "deceleration", "type": "float"}], "index": 4, "name": "decelerationChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "Qt3DCore::QEntity"}]}], "inputFile": "qabstractcameracontroller.h", "outputRevision": 69}, {"classes": [{"className": "QAbstractSpriteSheet", "lineNumber": 19, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "texture", "notify": "textureChanged", "read": "texture", "required": false, "scriptable": true, "stored": true, "type": "Qt3DRender::QAbstractTexture*", "user": false, "write": "setTexture"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "textureTransform", "notify": "textureTransformChanged", "read": "textureTransform", "required": false, "scriptable": true, "stored": true, "type": "QMatrix3x3", "user": false}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "currentIndex", "notify": "currentIndexChanged", "read": "currentIndex", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setCurrentIndex"}], "qualifiedClassName": "Qt3DExtras::QAbstractSpriteSheet", "signals": [{"access": "public", "arguments": [{"name": "texture", "type": "Qt3DRender::QAbstractTexture*"}], "index": 0, "name": "textureChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "textureTransform", "type": "QMatrix3x3"}], "index": 1, "name": "textureTransformChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "currentIndex", "type": "int"}], "index": 2, "name": "currentIndexChanged", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "texture", "type": "Qt3DRender::QAbstractTexture*"}], "index": 3, "name": "setTexture", "returnType": "void"}, {"access": "public", "arguments": [{"name": "currentIndex", "type": "int"}], "index": 4, "name": "setCurrentIndex", "returnType": "void"}], "superClasses": [{"access": "public", "name": "Qt3DCore::QNode"}]}], "inputFile": "qabstractspritesheet.h", "outputRevision": 69}, {"classes": [{"className": "QConeGeometry", "lineNumber": 20, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "hasTopEndcap", "notify": "hasTopEndcapChanged", "read": "hasTopEndcap", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setHasTopEndcap"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "hasBottomEndcap", "notify": "hasBottomEndcapChanged", "read": "hasBottomEndcap", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setHasBottomEndcap"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "rings", "notify": "ringsChanged", "read": "rings", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setRings"}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "slices", "notify": "slicesChanged", "read": "slices", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setSlices"}, {"constant": false, "designable": true, "final": false, "index": 4, "name": "topRadius", "notify": "topRadiusChanged", "read": "topRadius", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setTopRadius"}, {"constant": false, "designable": true, "final": false, "index": 5, "name": "bottomRadius", "notify": "bottomRadiusChanged", "read": "bottomRadius", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setBottomRadius"}, {"constant": false, "designable": true, "final": false, "index": 6, "name": "length", "notify": "lengthChanged", "read": "length", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "<PERSON><PERSON><PERSON><PERSON>"}, {"constant": true, "designable": true, "final": false, "index": 7, "name": "positionAttribute", "read": "positionAttribute", "required": false, "scriptable": true, "stored": true, "type": "Qt3DCore::QAttribute*", "user": false}, {"constant": true, "designable": true, "final": false, "index": 8, "name": "normalAttribute", "read": "normalAttribute", "required": false, "scriptable": true, "stored": true, "type": "Qt3DCore::QAttribute*", "user": false}, {"constant": true, "designable": true, "final": false, "index": 9, "name": "texCoordAttribute", "read": "texCoordAttribute", "required": false, "scriptable": true, "stored": true, "type": "Qt3DCore::QAttribute*", "user": false}, {"constant": true, "designable": true, "final": false, "index": 10, "name": "indexAttribute", "read": "indexAttribute", "required": false, "scriptable": true, "stored": true, "type": "Qt3DCore::QAttribute*", "user": false}], "qualifiedClassName": "Qt3DExtras::QConeGeometry", "signals": [{"access": "public", "arguments": [{"name": "hasTopEndcap", "type": "bool"}], "index": 0, "name": "hasTopEndcapChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "hasBottomEndcap", "type": "bool"}], "index": 1, "name": "hasBottomEndcapChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "topRadius", "type": "float"}], "index": 2, "name": "topRadiusChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "bottomRadius", "type": "float"}], "index": 3, "name": "bottomRadiusChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "rings", "type": "int"}], "index": 4, "name": "ringsChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "slices", "type": "int"}], "index": 5, "name": "slicesChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "length", "type": "float"}], "index": 6, "name": "lengthChanged", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "hasTopEndcap", "type": "bool"}], "index": 7, "name": "setHasTopEndcap", "returnType": "void"}, {"access": "public", "arguments": [{"name": "hasBottomEndcap", "type": "bool"}], "index": 8, "name": "setHasBottomEndcap", "returnType": "void"}, {"access": "public", "arguments": [{"name": "topRadius", "type": "float"}], "index": 9, "name": "setTopRadius", "returnType": "void"}, {"access": "public", "arguments": [{"name": "bottomRadius", "type": "float"}], "index": 10, "name": "setBottomRadius", "returnType": "void"}, {"access": "public", "arguments": [{"name": "rings", "type": "int"}], "index": 11, "name": "setRings", "returnType": "void"}, {"access": "public", "arguments": [{"name": "slices", "type": "int"}], "index": 12, "name": "setSlices", "returnType": "void"}, {"access": "public", "arguments": [{"name": "length", "type": "float"}], "index": 13, "name": "<PERSON><PERSON><PERSON><PERSON>", "returnType": "void"}], "superClasses": [{"access": "public", "name": "Qt3DCore::QGeometry"}]}], "inputFile": "qconegeometry.h", "outputRevision": 69}, {"classes": [{"className": "QConeGeometryView", "lineNumber": 14, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "rings", "notify": "ringsChanged", "read": "rings", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setRings"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "slices", "notify": "slicesChanged", "read": "slices", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setSlices"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "hasTopEndcap", "notify": "hasTopEndcapChanged", "read": "hasTopEndcap", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setHasTopEndcap"}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "hasBottomEndcap", "notify": "hasBottomEndcapChanged", "read": "hasBottomEndcap", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setHasBottomEndcap"}, {"constant": false, "designable": true, "final": false, "index": 4, "name": "topRadius", "notify": "topRadiusChanged", "read": "topRadius", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setTopRadius"}, {"constant": false, "designable": true, "final": false, "index": 5, "name": "bottomRadius", "notify": "bottomRadiusChanged", "read": "bottomRadius", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setBottomRadius"}, {"constant": false, "designable": true, "final": false, "index": 6, "name": "length", "notify": "lengthChanged", "read": "length", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "<PERSON><PERSON><PERSON><PERSON>"}], "qualifiedClassName": "Qt3DExtras::QConeGeometryView", "signals": [{"access": "public", "arguments": [{"name": "hasTopEndcap", "type": "bool"}], "index": 0, "name": "hasTopEndcapChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "hasBottomEndcap", "type": "bool"}], "index": 1, "name": "hasBottomEndcapChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "topRadius", "type": "float"}], "index": 2, "name": "topRadiusChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "bottomRadius", "type": "float"}], "index": 3, "name": "bottomRadiusChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "rings", "type": "int"}], "index": 4, "name": "ringsChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "slices", "type": "int"}], "index": 5, "name": "slicesChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "length", "type": "float"}], "index": 6, "name": "lengthChanged", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "hasTopEndcap", "type": "bool"}], "index": 7, "name": "setHasTopEndcap", "returnType": "void"}, {"access": "public", "arguments": [{"name": "hasBottomEndcap", "type": "bool"}], "index": 8, "name": "setHasBottomEndcap", "returnType": "void"}, {"access": "public", "arguments": [{"name": "topRadius", "type": "float"}], "index": 9, "name": "setTopRadius", "returnType": "void"}, {"access": "public", "arguments": [{"name": "bottomRadius", "type": "float"}], "index": 10, "name": "setBottomRadius", "returnType": "void"}, {"access": "public", "arguments": [{"name": "rings", "type": "int"}], "index": 11, "name": "setRings", "returnType": "void"}, {"access": "public", "arguments": [{"name": "slices", "type": "int"}], "index": 12, "name": "setSlices", "returnType": "void"}, {"access": "public", "arguments": [{"name": "length", "type": "float"}], "index": 13, "name": "<PERSON><PERSON><PERSON><PERSON>", "returnType": "void"}], "superClasses": [{"access": "public", "name": "Qt3DCore::QGeometryView"}]}], "inputFile": "qconegeometryview.h", "outputRevision": 69}, {"classes": [{"className": "QConeMesh", "lineNumber": 14, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "rings", "notify": "ringsChanged", "read": "rings", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setRings"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "slices", "notify": "slicesChanged", "read": "slices", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setSlices"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "hasTopEndcap", "notify": "hasTopEndcapChanged", "read": "hasTopEndcap", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setHasTopEndcap"}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "hasBottomEndcap", "notify": "hasBottomEndcapChanged", "read": "hasBottomEndcap", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setHasBottomEndcap"}, {"constant": false, "designable": true, "final": false, "index": 4, "name": "topRadius", "notify": "topRadiusChanged", "read": "topRadius", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setTopRadius"}, {"constant": false, "designable": true, "final": false, "index": 5, "name": "bottomRadius", "notify": "bottomRadiusChanged", "read": "bottomRadius", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setBottomRadius"}, {"constant": false, "designable": true, "final": false, "index": 6, "name": "length", "notify": "lengthChanged", "read": "length", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "<PERSON><PERSON><PERSON><PERSON>"}], "qualifiedClassName": "Qt3DExtras::QConeMesh", "signals": [{"access": "public", "arguments": [{"name": "hasTopEndcap", "type": "bool"}], "index": 0, "name": "hasTopEndcapChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "hasBottomEndcap", "type": "bool"}], "index": 1, "name": "hasBottomEndcapChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "topRadius", "type": "float"}], "index": 2, "name": "topRadiusChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "bottomRadius", "type": "float"}], "index": 3, "name": "bottomRadiusChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "rings", "type": "int"}], "index": 4, "name": "ringsChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "slices", "type": "int"}], "index": 5, "name": "slicesChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "length", "type": "float"}], "index": 6, "name": "lengthChanged", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "hasTopEndcap", "type": "bool"}], "index": 7, "name": "setHasTopEndcap", "returnType": "void"}, {"access": "public", "arguments": [{"name": "hasBottomEndcap", "type": "bool"}], "index": 8, "name": "setHasBottomEndcap", "returnType": "void"}, {"access": "public", "arguments": [{"name": "topRadius", "type": "float"}], "index": 9, "name": "setTopRadius", "returnType": "void"}, {"access": "public", "arguments": [{"name": "bottomRadius", "type": "float"}], "index": 10, "name": "setBottomRadius", "returnType": "void"}, {"access": "public", "arguments": [{"name": "rings", "type": "int"}], "index": 11, "name": "setRings", "returnType": "void"}, {"access": "public", "arguments": [{"name": "slices", "type": "int"}], "index": 12, "name": "setSlices", "returnType": "void"}, {"access": "public", "arguments": [{"name": "length", "type": "float"}], "index": 13, "name": "<PERSON><PERSON><PERSON><PERSON>", "returnType": "void"}], "superClasses": [{"access": "public", "name": "Qt3DRender::QGeo<PERSON><PERSON><PERSON><PERSON>"}]}], "inputFile": "qconemesh.h", "outputRevision": 69}, {"classes": [{"className": "QCuboidGeometry", "lineNumber": 23, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "xExtent", "notify": "xExtentChanged", "read": "xExtent", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setXExtent"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "yExtent", "notify": "yExtentChanged", "read": "yExtent", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setYExtent"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "zExtent", "notify": "zExtentChanged", "read": "zExtent", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setZExtent"}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "xyMeshResolution", "notify": "xyMeshResolutionChanged", "read": "xyMeshResolution", "required": false, "scriptable": true, "stored": true, "type": "QSize", "user": false, "write": "setXYMeshResolution"}, {"constant": false, "designable": true, "final": false, "index": 4, "name": "yzMeshResolution", "notify": "yzMeshResolutionChanged", "read": "yzMeshResolution", "required": false, "scriptable": true, "stored": true, "type": "QSize", "user": false, "write": "setYZMeshResolution"}, {"constant": false, "designable": true, "final": false, "index": 5, "name": "xzMeshResolution", "notify": "xzMeshResolutionChanged", "read": "xzMeshResolution", "required": false, "scriptable": true, "stored": true, "type": "QSize", "user": false, "write": "setXZMeshResolution"}, {"constant": true, "designable": true, "final": false, "index": 6, "name": "positionAttribute", "read": "positionAttribute", "required": false, "scriptable": true, "stored": true, "type": "Qt3DCore::QAttribute*", "user": false}, {"constant": true, "designable": true, "final": false, "index": 7, "name": "normalAttribute", "read": "normalAttribute", "required": false, "scriptable": true, "stored": true, "type": "Qt3DCore::QAttribute*", "user": false}, {"constant": true, "designable": true, "final": false, "index": 8, "name": "texCoordAttribute", "read": "texCoordAttribute", "required": false, "scriptable": true, "stored": true, "type": "Qt3DCore::QAttribute*", "user": false}, {"constant": true, "designable": true, "final": false, "index": 9, "name": "tangentAttribute", "read": "tangentAttribute", "required": false, "scriptable": true, "stored": true, "type": "Qt3DCore::QAttribute*", "user": false}, {"constant": true, "designable": true, "final": false, "index": 10, "name": "indexAttribute", "read": "indexAttribute", "required": false, "scriptable": true, "stored": true, "type": "Qt3DCore::QAttribute*", "user": false}], "qualifiedClassName": "Qt3DExtras::QCuboidGeometry", "signals": [{"access": "public", "arguments": [{"name": "xExtent", "type": "float"}], "index": 0, "name": "xExtentChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "yExtent", "type": "float"}], "index": 1, "name": "yExtentChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "zExtent", "type": "float"}], "index": 2, "name": "zExtentChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "yzMeshResolution", "type": "QSize"}], "index": 3, "name": "yzMeshResolutionChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "xzMeshResolution", "type": "QSize"}], "index": 4, "name": "xzMeshResolutionChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "xyMeshResolution", "type": "QSize"}], "index": 5, "name": "xyMeshResolutionChanged", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "xExtent", "type": "float"}], "index": 6, "name": "setXExtent", "returnType": "void"}, {"access": "public", "arguments": [{"name": "yExtent", "type": "float"}], "index": 7, "name": "setYExtent", "returnType": "void"}, {"access": "public", "arguments": [{"name": "zExtent", "type": "float"}], "index": 8, "name": "setZExtent", "returnType": "void"}, {"access": "public", "arguments": [{"name": "resolution", "type": "QSize"}], "index": 9, "name": "setYZMeshResolution", "returnType": "void"}, {"access": "public", "arguments": [{"name": "resolution", "type": "QSize"}], "index": 10, "name": "setXZMeshResolution", "returnType": "void"}, {"access": "public", "arguments": [{"name": "resolution", "type": "QSize"}], "index": 11, "name": "setXYMeshResolution", "returnType": "void"}], "superClasses": [{"access": "public", "name": "Qt3DCore::QGeometry"}]}], "inputFile": "qcuboidgeometry.h", "outputRevision": 69}, {"classes": [{"className": "QCuboidGeometryView", "lineNumber": 15, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "xExtent", "notify": "xExtentChanged", "read": "xExtent", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setXExtent"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "yExtent", "notify": "yExtentChanged", "read": "yExtent", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setYExtent"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "zExtent", "notify": "zExtentChanged", "read": "zExtent", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setZExtent"}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "yzMeshResolution", "notify": "yzMeshResolutionChanged", "read": "yzMeshResolution", "required": false, "scriptable": true, "stored": true, "type": "QSize", "user": false, "write": "setYZMeshResolution"}, {"constant": false, "designable": true, "final": false, "index": 4, "name": "xzMeshResolution", "notify": "xzMeshResolutionChanged", "read": "xzMeshResolution", "required": false, "scriptable": true, "stored": true, "type": "QSize", "user": false, "write": "setXZMeshResolution"}, {"constant": false, "designable": true, "final": false, "index": 5, "name": "xyMeshResolution", "notify": "xyMeshResolutionChanged", "read": "xyMeshResolution", "required": false, "scriptable": true, "stored": true, "type": "QSize", "user": false, "write": "setXYMeshResolution"}], "qualifiedClassName": "Qt3DExtras::QCuboidGeometryView", "signals": [{"access": "public", "arguments": [{"name": "xExtent", "type": "float"}], "index": 0, "name": "xExtentChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "yExtent", "type": "float"}], "index": 1, "name": "yExtentChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "zExtent", "type": "float"}], "index": 2, "name": "zExtentChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "yzMeshResolution", "type": "QSize"}], "index": 3, "name": "yzMeshResolutionChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "xzMeshResolution", "type": "QSize"}], "index": 4, "name": "xzMeshResolutionChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "xyMeshResolution", "type": "QSize"}], "index": 5, "name": "xyMeshResolutionChanged", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "xExtent", "type": "float"}], "index": 6, "name": "setXExtent", "returnType": "void"}, {"access": "public", "arguments": [{"name": "yExtent", "type": "float"}], "index": 7, "name": "setYExtent", "returnType": "void"}, {"access": "public", "arguments": [{"name": "zExtent", "type": "float"}], "index": 8, "name": "setZExtent", "returnType": "void"}, {"access": "public", "arguments": [{"name": "resolution", "type": "QSize"}], "index": 9, "name": "setYZMeshResolution", "returnType": "void"}, {"access": "public", "arguments": [{"name": "resolution", "type": "QSize"}], "index": 10, "name": "setXZMeshResolution", "returnType": "void"}, {"access": "public", "arguments": [{"name": "resolution", "type": "QSize"}], "index": 11, "name": "setXYMeshResolution", "returnType": "void"}], "superClasses": [{"access": "public", "name": "Qt3DCore::QGeometryView"}]}], "inputFile": "qcuboidgeometryview.h", "outputRevision": 69}, {"classes": [{"className": "QCuboidMesh", "lineNumber": 15, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "xExtent", "notify": "xExtentChanged", "read": "xExtent", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setXExtent"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "yExtent", "notify": "yExtentChanged", "read": "yExtent", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setYExtent"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "zExtent", "notify": "zExtentChanged", "read": "zExtent", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setZExtent"}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "yzMeshResolution", "notify": "yzMeshResolutionChanged", "read": "yzMeshResolution", "required": false, "scriptable": true, "stored": true, "type": "QSize", "user": false, "write": "setYZMeshResolution"}, {"constant": false, "designable": true, "final": false, "index": 4, "name": "xzMeshResolution", "notify": "xzMeshResolutionChanged", "read": "xzMeshResolution", "required": false, "scriptable": true, "stored": true, "type": "QSize", "user": false, "write": "setXZMeshResolution"}, {"constant": false, "designable": true, "final": false, "index": 5, "name": "xyMeshResolution", "notify": "xyMeshResolutionChanged", "read": "xyMeshResolution", "required": false, "scriptable": true, "stored": true, "type": "QSize", "user": false, "write": "setXYMeshResolution"}], "qualifiedClassName": "Qt3DExtras::QCuboidMesh", "signals": [{"access": "public", "arguments": [{"name": "xExtent", "type": "float"}], "index": 0, "name": "xExtentChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "yExtent", "type": "float"}], "index": 1, "name": "yExtentChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "zExtent", "type": "float"}], "index": 2, "name": "zExtentChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "yzMeshResolution", "type": "QSize"}], "index": 3, "name": "yzMeshResolutionChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "xzMeshResolution", "type": "QSize"}], "index": 4, "name": "xzMeshResolutionChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "xyMeshResolution", "type": "QSize"}], "index": 5, "name": "xyMeshResolutionChanged", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "xExtent", "type": "float"}], "index": 6, "name": "setXExtent", "returnType": "void"}, {"access": "public", "arguments": [{"name": "yExtent", "type": "float"}], "index": 7, "name": "setYExtent", "returnType": "void"}, {"access": "public", "arguments": [{"name": "zExtent", "type": "float"}], "index": 8, "name": "setZExtent", "returnType": "void"}, {"access": "public", "arguments": [{"name": "resolution", "type": "QSize"}], "index": 9, "name": "setYZMeshResolution", "returnType": "void"}, {"access": "public", "arguments": [{"name": "resolution", "type": "QSize"}], "index": 10, "name": "setXZMeshResolution", "returnType": "void"}, {"access": "public", "arguments": [{"name": "resolution", "type": "QSize"}], "index": 11, "name": "setXYMeshResolution", "returnType": "void"}], "superClasses": [{"access": "public", "name": "Qt3DRender::QGeo<PERSON><PERSON><PERSON><PERSON>"}]}], "inputFile": "qcuboidmesh.h", "outputRevision": 69}, {"classes": [{"className": "QCylinderGeometry", "lineNumber": 21, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "rings", "notify": "ringsChanged", "read": "rings", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setRings"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "slices", "notify": "slicesChanged", "read": "slices", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setSlices"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "radius", "notify": "radiusChanged", "read": "radius", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setRadius"}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "length", "notify": "lengthChanged", "read": "length", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "<PERSON><PERSON><PERSON><PERSON>"}, {"constant": true, "designable": true, "final": false, "index": 4, "name": "positionAttribute", "read": "positionAttribute", "required": false, "scriptable": true, "stored": true, "type": "Qt3DCore::QAttribute*", "user": false}, {"constant": true, "designable": true, "final": false, "index": 5, "name": "normalAttribute", "read": "normalAttribute", "required": false, "scriptable": true, "stored": true, "type": "Qt3DCore::QAttribute*", "user": false}, {"constant": true, "designable": true, "final": false, "index": 6, "name": "texCoordAttribute", "read": "texCoordAttribute", "required": false, "scriptable": true, "stored": true, "type": "Qt3DCore::QAttribute*", "user": false}, {"constant": true, "designable": true, "final": false, "index": 7, "name": "indexAttribute", "read": "indexAttribute", "required": false, "scriptable": true, "stored": true, "type": "Qt3DCore::QAttribute*", "user": false}], "qualifiedClassName": "Qt3DExtras::QCylinderGeometry", "signals": [{"access": "public", "arguments": [{"name": "radius", "type": "float"}], "index": 0, "name": "radiusChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "rings", "type": "int"}], "index": 1, "name": "ringsChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "slices", "type": "int"}], "index": 2, "name": "slicesChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "length", "type": "float"}], "index": 3, "name": "lengthChanged", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "rings", "type": "int"}], "index": 4, "name": "setRings", "returnType": "void"}, {"access": "public", "arguments": [{"name": "slices", "type": "int"}], "index": 5, "name": "setSlices", "returnType": "void"}, {"access": "public", "arguments": [{"name": "radius", "type": "float"}], "index": 6, "name": "setRadius", "returnType": "void"}, {"access": "public", "arguments": [{"name": "length", "type": "float"}], "index": 7, "name": "<PERSON><PERSON><PERSON><PERSON>", "returnType": "void"}], "superClasses": [{"access": "public", "name": "Qt3DCore::QGeometry"}]}], "inputFile": "qcylindergeometry.h", "outputRevision": 69}, {"classes": [{"className": "QCylinderGeometryView", "lineNumber": 15, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "rings", "notify": "ringsChanged", "read": "rings", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setRings"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "slices", "notify": "slicesChanged", "read": "slices", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setSlices"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "radius", "notify": "radiusChanged", "read": "radius", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setRadius"}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "length", "notify": "lengthChanged", "read": "length", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "<PERSON><PERSON><PERSON><PERSON>"}], "qualifiedClassName": "Qt3DExtras::QCylinderGeometryView", "signals": [{"access": "public", "arguments": [{"name": "radius", "type": "float"}], "index": 0, "name": "radiusChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "rings", "type": "int"}], "index": 1, "name": "ringsChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "slices", "type": "int"}], "index": 2, "name": "slicesChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "length", "type": "float"}], "index": 3, "name": "lengthChanged", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "rings", "type": "int"}], "index": 4, "name": "setRings", "returnType": "void"}, {"access": "public", "arguments": [{"name": "slices", "type": "int"}], "index": 5, "name": "setSlices", "returnType": "void"}, {"access": "public", "arguments": [{"name": "radius", "type": "float"}], "index": 6, "name": "setRadius", "returnType": "void"}, {"access": "public", "arguments": [{"name": "length", "type": "float"}], "index": 7, "name": "<PERSON><PERSON><PERSON><PERSON>", "returnType": "void"}], "superClasses": [{"access": "public", "name": "Qt3DCore::QGeometryView"}]}], "inputFile": "qcylindergeometryview.h", "outputRevision": 69}, {"classes": [{"className": "<PERSON><PERSON><PERSON><PERSON>", "lineNumber": 15, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "rings", "notify": "ringsChanged", "read": "rings", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setRings"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "slices", "notify": "slicesChanged", "read": "slices", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setSlices"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "radius", "notify": "radiusChanged", "read": "radius", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setRadius"}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "length", "notify": "lengthChanged", "read": "length", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "<PERSON><PERSON><PERSON><PERSON>"}], "qualifiedClassName": "Qt3DExtras::<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "signals": [{"access": "public", "arguments": [{"name": "radius", "type": "float"}], "index": 0, "name": "radiusChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "rings", "type": "int"}], "index": 1, "name": "ringsChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "slices", "type": "int"}], "index": 2, "name": "slicesChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "length", "type": "float"}], "index": 3, "name": "lengthChanged", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "rings", "type": "int"}], "index": 4, "name": "setRings", "returnType": "void"}, {"access": "public", "arguments": [{"name": "slices", "type": "int"}], "index": 5, "name": "setSlices", "returnType": "void"}, {"access": "public", "arguments": [{"name": "radius", "type": "float"}], "index": 6, "name": "setRadius", "returnType": "void"}, {"access": "public", "arguments": [{"name": "length", "type": "float"}], "index": 7, "name": "<PERSON><PERSON><PERSON><PERSON>", "returnType": "void"}], "superClasses": [{"access": "public", "name": "Qt3DRender::QGeo<PERSON><PERSON><PERSON><PERSON>"}]}], "inputFile": "qcylindermesh.h", "outputRevision": 69}, {"classes": [{"className": "QDiffuseMapMaterial", "lineNumber": 23, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "ambient", "notify": "ambientChanged", "read": "ambient", "required": false, "scriptable": true, "stored": true, "type": "QColor", "user": false, "write": "setAmbient"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "specular", "notify": "specularChanged", "read": "specular", "required": false, "scriptable": true, "stored": true, "type": "QColor", "user": false, "write": "setSpecular"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "shininess", "notify": "shininessChanged", "read": "shininess", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setShininess"}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "diffuse", "notify": "diffuseChanged", "read": "diffuse", "required": false, "scriptable": true, "stored": true, "type": "Qt3DRender::QAbstractTexture*", "user": false, "write": "setDiffuse"}, {"constant": false, "designable": true, "final": false, "index": 4, "name": "textureScale", "notify": "textureScaleChanged", "read": "textureScale", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setTextureScale"}], "qualifiedClassName": "Qt3DExtras::QDiffuseMapMaterial", "signals": [{"access": "public", "arguments": [{"name": "ambient", "type": "QColor"}], "index": 0, "name": "ambientChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "diffuse", "type": "Qt3DRender::QAbstractTexture*"}], "index": 1, "name": "diffuseChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "specular", "type": "QColor"}], "index": 2, "name": "specularChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "shininess", "type": "float"}], "index": 3, "name": "shininessChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "textureScale", "type": "float"}], "index": 4, "name": "textureScaleChanged", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "color", "type": "QColor"}], "index": 5, "name": "setAmbient", "returnType": "void"}, {"access": "public", "arguments": [{"name": "specular", "type": "QColor"}], "index": 6, "name": "setSpecular", "returnType": "void"}, {"access": "public", "arguments": [{"name": "shininess", "type": "float"}], "index": 7, "name": "setShininess", "returnType": "void"}, {"access": "public", "arguments": [{"name": "diffuse", "type": "Qt3DRender::QAbstractTexture*"}], "index": 8, "name": "setDiffuse", "returnType": "void"}, {"access": "public", "arguments": [{"name": "textureScale", "type": "float"}], "index": 9, "name": "setTextureScale", "returnType": "void"}], "superClasses": [{"access": "public", "name": "Qt3DRender::QMaterial"}]}], "inputFile": "qdiffusemapmaterial.h", "outputRevision": 69}, {"classes": [{"className": "QDiffuseSpecularMapMaterial", "lineNumber": 22, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "ambient", "notify": "ambientChanged", "read": "ambient", "required": false, "scriptable": true, "stored": true, "type": "QColor", "user": false, "write": "setAmbient"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "shininess", "notify": "shininessChanged", "read": "shininess", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setShininess"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "specular", "notify": "specularChanged", "read": "specular", "required": false, "scriptable": true, "stored": true, "type": "Qt3DRender::QAbstractTexture*", "user": false, "write": "setSpecular"}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "diffuse", "notify": "diffuseChanged", "read": "diffuse", "required": false, "scriptable": true, "stored": true, "type": "Qt3DRender::QAbstractTexture*", "user": false, "write": "setDiffuse"}, {"constant": false, "designable": true, "final": false, "index": 4, "name": "textureScale", "notify": "textureScaleChanged", "read": "textureScale", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setTextureScale"}], "qualifiedClassName": "Qt3DExtras::QDiffuseSpecularMapMaterial", "signals": [{"access": "public", "arguments": [{"name": "ambient", "type": "QColor"}], "index": 0, "name": "ambientChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "diffuse", "type": "Qt3DRender::QAbstractTexture*"}], "index": 1, "name": "diffuseChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "specular", "type": "Qt3DRender::QAbstractTexture*"}], "index": 2, "name": "specularChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "shininess", "type": "float"}], "index": 3, "name": "shininessChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "textureScale", "type": "float"}], "index": 4, "name": "textureScaleChanged", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "ambient", "type": "QColor"}], "index": 5, "name": "setAmbient", "returnType": "void"}, {"access": "public", "arguments": [{"name": "diffuse", "type": "Qt3DRender::QAbstractTexture*"}], "index": 6, "name": "setDiffuse", "returnType": "void"}, {"access": "public", "arguments": [{"name": "specular", "type": "Qt3DRender::QAbstractTexture*"}], "index": 7, "name": "setSpecular", "returnType": "void"}, {"access": "public", "arguments": [{"name": "shininess", "type": "float"}], "index": 8, "name": "setShininess", "returnType": "void"}, {"access": "public", "arguments": [{"name": "textureScale", "type": "float"}], "index": 9, "name": "setTextureScale", "returnType": "void"}], "superClasses": [{"access": "public", "name": "Qt3DRender::QMaterial"}]}], "inputFile": "qdiffusespecularmapmaterial.h", "outputRevision": 69}, {"classes": [{"className": "QDiffuseSpecularMaterial", "lineNumber": 17, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "ambient", "notify": "ambientChanged", "read": "ambient", "required": false, "scriptable": true, "stored": true, "type": "QColor", "user": false, "write": "setAmbient"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "diffuse", "notify": "diffuseChanged", "read": "diffuse", "required": false, "scriptable": true, "stored": true, "type": "Q<PERSON><PERSON><PERSON>", "user": false, "write": "setDiffuse"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "specular", "notify": "specularChanged", "read": "specular", "required": false, "scriptable": true, "stored": true, "type": "Q<PERSON><PERSON><PERSON>", "user": false, "write": "setSpecular"}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "shininess", "notify": "shininessChanged", "read": "shininess", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setShininess"}, {"constant": false, "designable": true, "final": false, "index": 4, "name": "normal", "notify": "normalChanged", "read": "normal", "required": false, "scriptable": true, "stored": true, "type": "Q<PERSON><PERSON><PERSON>", "user": false, "write": "setNormal"}, {"constant": false, "designable": true, "final": false, "index": 5, "name": "textureScale", "notify": "textureScaleChanged", "read": "textureScale", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setTextureScale"}, {"constant": false, "designable": true, "final": false, "index": 6, "name": "alphaBlending", "notify": "alphaBlendingEnabledChanged", "read": "isAlphaBlendingEnabled", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setAlphaBlendingEnabled"}], "qualifiedClassName": "Qt3DExtras::QDiffuseSpecularMaterial", "signals": [{"access": "public", "arguments": [{"name": "ambient", "type": "QColor"}], "index": 0, "name": "ambientChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "diffuse", "type": "Q<PERSON><PERSON><PERSON>"}], "index": 1, "name": "diffuseChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "specular", "type": "Q<PERSON><PERSON><PERSON>"}], "index": 2, "name": "specularChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "shininess", "type": "float"}], "index": 3, "name": "shininessChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "normal", "type": "Q<PERSON><PERSON><PERSON>"}], "index": 4, "name": "normalChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "textureScale", "type": "float"}], "index": 5, "name": "textureScaleChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "enabled", "type": "bool"}], "index": 6, "name": "alphaBlendingEnabledChanged", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "ambient", "type": "QColor"}], "index": 7, "name": "setAmbient", "returnType": "void"}, {"access": "public", "arguments": [{"name": "diffuse", "type": "Q<PERSON><PERSON><PERSON>"}], "index": 8, "name": "setDiffuse", "returnType": "void"}, {"access": "public", "arguments": [{"name": "specular", "type": "Q<PERSON><PERSON><PERSON>"}], "index": 9, "name": "setSpecular", "returnType": "void"}, {"access": "public", "arguments": [{"name": "shininess", "type": "float"}], "index": 10, "name": "setShininess", "returnType": "void"}, {"access": "public", "arguments": [{"name": "normal", "type": "Q<PERSON><PERSON><PERSON>"}], "index": 11, "name": "setNormal", "returnType": "void"}, {"access": "public", "arguments": [{"name": "textureScale", "type": "float"}], "index": 12, "name": "setTextureScale", "returnType": "void"}, {"access": "public", "arguments": [{"name": "enabled", "type": "bool"}], "index": 13, "name": "setAlphaBlendingEnabled", "returnType": "void"}], "superClasses": [{"access": "public", "name": "Qt3DRender::QMaterial"}]}], "inputFile": "qdiffusespecularmaterial.h", "outputRevision": 69}, {"classes": [{"className": "QExtrudedTextGeometry", "lineNumber": 24, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "text", "notify": "textChanged", "read": "text", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "setText"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "font", "notify": "fontChanged", "read": "font", "required": false, "scriptable": true, "stored": true, "type": "QFont", "user": false, "write": "setFont"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "extrusionLength", "notify": "depthChanged", "read": "extrusionLength", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "<PERSON><PERSON><PERSON><PERSON>"}, {"constant": true, "designable": true, "final": false, "index": 3, "name": "positionAttribute", "read": "positionAttribute", "required": false, "scriptable": true, "stored": true, "type": "Qt3DCore::QAttribute*", "user": false}, {"constant": true, "designable": true, "final": false, "index": 4, "name": "normalAttribute", "read": "normalAttribute", "required": false, "scriptable": true, "stored": true, "type": "Qt3DCore::QAttribute*", "user": false}, {"constant": true, "designable": true, "final": false, "index": 5, "name": "indexAttribute", "read": "indexAttribute", "required": false, "scriptable": true, "stored": true, "type": "Qt3DCore::QAttribute*", "user": false}], "qualifiedClassName": "Qt3DExtras::QExtrudedTextGeometry", "signals": [{"access": "public", "arguments": [{"name": "text", "type": "QString"}], "index": 0, "name": "textChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "font", "type": "QFont"}], "index": 1, "name": "fontChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "extrusionLength", "type": "float"}], "index": 2, "name": "depthChanged", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "text", "type": "QString"}], "index": 3, "name": "setText", "returnType": "void"}, {"access": "public", "arguments": [{"name": "font", "type": "QFont"}], "index": 4, "name": "setFont", "returnType": "void"}, {"access": "public", "arguments": [{"name": "extrusionLength", "type": "float"}], "index": 5, "name": "<PERSON><PERSON><PERSON><PERSON>", "returnType": "void"}], "superClasses": [{"access": "public", "name": "Qt3DCore::QGeometry"}]}], "inputFile": "qextrudedtextgeometry.h", "outputRevision": 69}, {"classes": [{"className": "QExtrudedTextMesh", "lineNumber": 16, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "text", "notify": "textChanged", "read": "text", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "setText"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "font", "notify": "fontChanged", "read": "font", "required": false, "scriptable": true, "stored": true, "type": "QFont", "user": false, "write": "setFont"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "depth", "notify": "depthChanged", "read": "depth", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "<PERSON><PERSON><PERSON><PERSON>"}], "qualifiedClassName": "Qt3DExtras::QExtrudedTextMesh", "signals": [{"access": "public", "arguments": [{"name": "text", "type": "QString"}], "index": 0, "name": "textChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "font", "type": "QFont"}], "index": 1, "name": "fontChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "depth", "type": "float"}], "index": 2, "name": "depthChanged", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "text", "type": "QString"}], "index": 3, "name": "setText", "returnType": "void"}, {"access": "public", "arguments": [{"name": "font", "type": "QFont"}], "index": 4, "name": "setFont", "returnType": "void"}, {"access": "public", "arguments": [{"name": "depth", "type": "float"}], "index": 5, "name": "<PERSON><PERSON><PERSON><PERSON>", "returnType": "void"}], "superClasses": [{"access": "public", "name": "Qt3DRender::QGeo<PERSON><PERSON><PERSON><PERSON>"}]}], "inputFile": "qextrudedtextmesh.h", "outputRevision": 69}, {"classes": [{"className": "QFirstPersonCameraController", "lineNumber": 13, "object": true, "qualifiedClassName": "Qt3DExtras::QFirstPersonCameraController", "superClasses": [{"access": "public", "fullyQualifiedName": "Qt3DExtras::QAbstractCameraController", "name": "QAbstractCameraController"}]}], "inputFile": "qfirstpersoncameracontroller.h", "outputRevision": 69}, {"classes": [{"className": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "lineNumber": 22, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "surface", "notify": "surfaceChanged", "read": "surface", "required": false, "scriptable": true, "stored": true, "type": "QObject*", "user": false, "write": "setSurface"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "window", "notify": "surfaceChanged", "read": "surface", "required": false, "scriptable": true, "stored": true, "type": "QObject*", "user": false, "write": "setSurface"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "viewportRect", "notify": "viewportRectChanged", "read": "viewportRect", "required": false, "scriptable": true, "stored": true, "type": "QRectF", "user": false, "write": "setViewportRect"}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "clearColor", "notify": "clearColorChanged", "read": "clearColor", "required": false, "scriptable": true, "stored": true, "type": "QColor", "user": false, "write": "setClearColor"}, {"constant": false, "designable": true, "final": false, "index": 4, "name": "buffersToClear", "notify": "buffersToClearChanged", "read": "buffersToClear", "required": false, "revision": 65294, "scriptable": true, "stored": true, "type": "Qt3DRender::QClearBuffers::BufferType", "user": false, "write": "setBuffersToClear"}, {"constant": false, "designable": true, "final": false, "index": 5, "name": "camera", "notify": "cameraChanged", "read": "camera", "required": false, "scriptable": true, "stored": true, "type": "Qt3DCore::QEntity*", "user": false, "write": "setCamera"}, {"constant": false, "designable": true, "final": false, "index": 6, "name": "externalRenderTargetSize", "notify": "externalRenderTargetSizeChanged", "read": "externalRenderTargetSize", "required": false, "scriptable": true, "stored": true, "type": "QSize", "user": false, "write": "setExternalRenderTargetSize"}, {"constant": false, "designable": true, "final": false, "index": 7, "name": "frustumCulling", "notify": "frustumCullingEnabledChanged", "read": "isFrustumCullingEnabled", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setFrustumCullingEnabled"}, {"constant": false, "designable": true, "final": false, "index": 8, "name": "gamma", "notify": "gammaChanged", "read": "gamma", "required": false, "revision": 65289, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "<PERSON><PERSON><PERSON><PERSON>"}, {"constant": false, "designable": true, "final": false, "index": 9, "name": "showDebugOverlay", "notify": "showDebugOverlayChanged", "read": "showDebugOverlay", "required": false, "revision": 65295, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setShowDebugOverlay"}], "qualifiedClassName": "Qt3DExtras::<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "signals": [{"access": "public", "arguments": [{"name": "viewportRect", "type": "QRectF"}], "index": 0, "name": "viewportRectChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "clearColor", "type": "QColor"}], "index": 1, "name": "clearColorChanged", "returnType": "void"}, {"access": "public", "arguments": [{"type": "Qt3DRender::QClearBuffers::BufferType"}], "index": 2, "name": "buffersToClearChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "camera", "type": "Qt3DCore::QEntity*"}], "index": 3, "name": "cameraChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "surface", "type": "QObject*"}], "index": 4, "name": "surfaceChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "size", "type": "QSize"}], "index": 5, "name": "externalRenderTargetSizeChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "enabled", "type": "bool"}], "index": 6, "name": "frustumCullingEnabledChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "gamma", "type": "float"}], "index": 7, "name": "gammaChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "showDebugOverlay", "type": "bool"}], "index": 8, "name": "showDebugOverlayChanged", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "viewportRect", "type": "QRectF"}], "index": 9, "name": "setViewportRect", "returnType": "void"}, {"access": "public", "arguments": [{"name": "clearColor", "type": "QColor"}], "index": 10, "name": "setClearColor", "returnType": "void"}, {"access": "public", "arguments": [{"type": "Qt3DRender::QClearBuffers::BufferType"}], "index": 11, "name": "setBuffersToClear", "returnType": "void"}, {"access": "public", "arguments": [{"name": "camera", "type": "Qt3DCore::QEntity*"}], "index": 12, "name": "setCamera", "returnType": "void"}, {"access": "public", "arguments": [{"name": "surface", "type": "QObject*"}], "index": 13, "name": "setSurface", "returnType": "void"}, {"access": "public", "arguments": [{"name": "size", "type": "QSize"}], "index": 14, "name": "setExternalRenderTargetSize", "returnType": "void"}, {"access": "public", "arguments": [{"name": "enabled", "type": "bool"}], "index": 15, "name": "setFrustumCullingEnabled", "returnType": "void"}, {"access": "public", "arguments": [{"name": "gamma", "type": "float"}], "index": 16, "name": "<PERSON><PERSON><PERSON><PERSON>", "returnType": "void"}, {"access": "public", "arguments": [{"name": "showDebugOverlay", "type": "bool"}], "index": 17, "name": "setShowDebugOverlay", "returnType": "void"}], "superClasses": [{"access": "public", "name": "Qt3DRender::QTechniqueFilter"}]}], "inputFile": "qforwardrenderer.h", "outputRevision": 69}, {"classes": [{"className": "QGoochMaterial", "lineNumber": 17, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "diffuse", "notify": "diffuseChanged", "read": "diffuse", "required": false, "scriptable": true, "stored": true, "type": "QColor", "user": false, "write": "setDiffuse"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "specular", "notify": "specularChanged", "read": "specular", "required": false, "scriptable": true, "stored": true, "type": "QColor", "user": false, "write": "setSpecular"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "cool", "notify": "coolChanged", "read": "cool", "required": false, "scriptable": true, "stored": true, "type": "QColor", "user": false, "write": "setCool"}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "warm", "notify": "warmChanged", "read": "warm", "required": false, "scriptable": true, "stored": true, "type": "QColor", "user": false, "write": "setWarm"}, {"constant": false, "designable": true, "final": false, "index": 4, "name": "alpha", "notify": "alphaChanged", "read": "alpha", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "<PERSON><PERSON><PERSON><PERSON>"}, {"constant": false, "designable": true, "final": false, "index": 5, "name": "beta", "notify": "betaChanged", "read": "beta", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setBeta"}, {"constant": false, "designable": true, "final": false, "index": 6, "name": "shininess", "notify": "shininessChanged", "read": "shininess", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setShininess"}], "qualifiedClassName": "Qt3DExtras::QGoochMaterial", "signals": [{"access": "public", "arguments": [{"name": "diffuse", "type": "QColor"}], "index": 0, "name": "diffuseChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "specular", "type": "QColor"}], "index": 1, "name": "specularChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "cool", "type": "QColor"}], "index": 2, "name": "coolChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "warm", "type": "QColor"}], "index": 3, "name": "warmChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "alpha", "type": "float"}], "index": 4, "name": "alphaChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "beta", "type": "float"}], "index": 5, "name": "betaChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "shininess", "type": "float"}], "index": 6, "name": "shininessChanged", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "diffuse", "type": "QColor"}], "index": 7, "name": "setDiffuse", "returnType": "void"}, {"access": "public", "arguments": [{"name": "specular", "type": "QColor"}], "index": 8, "name": "setSpecular", "returnType": "void"}, {"access": "public", "arguments": [{"name": "cool", "type": "QColor"}], "index": 9, "name": "setCool", "returnType": "void"}, {"access": "public", "arguments": [{"name": "warm", "type": "QColor"}], "index": 10, "name": "setWarm", "returnType": "void"}, {"access": "public", "arguments": [{"name": "alpha", "type": "float"}], "index": 11, "name": "<PERSON><PERSON><PERSON><PERSON>", "returnType": "void"}, {"access": "public", "arguments": [{"name": "beta", "type": "float"}], "index": 12, "name": "setBeta", "returnType": "void"}, {"access": "public", "arguments": [{"name": "shininess", "type": "float"}], "index": 13, "name": "setShininess", "returnType": "void"}], "superClasses": [{"access": "public", "name": "Qt3DRender::QMaterial"}]}], "inputFile": "qgoochmaterial.h", "outputRevision": 69}, {"classes": [{"className": "QMetalRoughMaterial", "lineNumber": 21, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "baseColor", "notify": "baseColorChanged", "read": "baseColor", "required": false, "scriptable": true, "stored": true, "type": "Q<PERSON><PERSON><PERSON>", "user": false, "write": "setBaseColor"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "metalness", "notify": "metalnessChanged", "read": "metalness", "required": false, "scriptable": true, "stored": true, "type": "Q<PERSON><PERSON><PERSON>", "user": false, "write": "setMetalness"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "roughness", "notify": "roughnessChanged", "read": "roughness", "required": false, "scriptable": true, "stored": true, "type": "Q<PERSON><PERSON><PERSON>", "user": false, "write": "setRoughness"}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "ambientOcclusion", "notify": "ambientOcclusionChanged", "read": "ambientOcclusion", "required": false, "revision": 65290, "scriptable": true, "stored": true, "type": "Q<PERSON><PERSON><PERSON>", "user": false, "write": "setAmbientOcclusion"}, {"constant": false, "designable": true, "final": false, "index": 4, "name": "normal", "notify": "normalChanged", "read": "normal", "required": false, "revision": 65290, "scriptable": true, "stored": true, "type": "Q<PERSON><PERSON><PERSON>", "user": false, "write": "setNormal"}, {"constant": false, "designable": true, "final": false, "index": 5, "name": "textureScale", "notify": "textureScaleChanged", "read": "textureScale", "required": false, "revision": 65290, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setTextureScale"}], "qualifiedClassName": "Qt3DExtras::QMetalRoughMaterial", "signals": [{"access": "public", "arguments": [{"name": "baseColor", "type": "Q<PERSON><PERSON><PERSON>"}], "index": 0, "name": "baseColorChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "metalness", "type": "Q<PERSON><PERSON><PERSON>"}], "index": 1, "name": "metalnessChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "roughness", "type": "Q<PERSON><PERSON><PERSON>"}], "index": 2, "name": "roughnessChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "ambientOcclusion", "type": "Q<PERSON><PERSON><PERSON>"}], "index": 3, "name": "ambientOcclusionChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "normal", "type": "Q<PERSON><PERSON><PERSON>"}], "index": 4, "name": "normalChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "textureScale", "type": "float"}], "index": 5, "name": "textureScaleChanged", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "baseColor", "type": "Q<PERSON><PERSON><PERSON>"}], "index": 6, "name": "setBaseColor", "returnType": "void"}, {"access": "public", "arguments": [{"name": "metalness", "type": "Q<PERSON><PERSON><PERSON>"}], "index": 7, "name": "setMetalness", "returnType": "void"}, {"access": "public", "arguments": [{"name": "roughness", "type": "Q<PERSON><PERSON><PERSON>"}], "index": 8, "name": "setRoughness", "returnType": "void"}, {"access": "public", "arguments": [{"name": "ambientOcclusion", "type": "Q<PERSON><PERSON><PERSON>"}], "index": 9, "name": "setAmbientOcclusion", "returnType": "void"}, {"access": "public", "arguments": [{"name": "normal", "type": "Q<PERSON><PERSON><PERSON>"}], "index": 10, "name": "setNormal", "returnType": "void"}, {"access": "public", "arguments": [{"name": "textureScale", "type": "float"}], "index": 11, "name": "setTextureScale", "returnType": "void"}], "superClasses": [{"access": "public", "name": "Qt3DRender::QMaterial"}]}], "inputFile": "qmetalroughmaterial.h", "outputRevision": 69}, {"classes": [{"className": "QMorphPhongMaterial", "lineNumber": 17, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "ambient", "notify": "ambientChanged", "read": "ambient", "required": false, "scriptable": true, "stored": true, "type": "QColor", "user": false, "write": "setAmbient"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "diffuse", "notify": "diffuseChanged", "read": "diffuse", "required": false, "scriptable": true, "stored": true, "type": "QColor", "user": false, "write": "setDiffuse"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "specular", "notify": "specularChanged", "read": "specular", "required": false, "scriptable": true, "stored": true, "type": "QColor", "user": false, "write": "setSpecular"}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "shininess", "notify": "shininessChanged", "read": "shininess", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setShininess"}, {"constant": false, "designable": true, "final": false, "index": 4, "name": "interpolator", "notify": "interpolatorChanged", "read": "interpolator", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setInterpolator"}], "qualifiedClassName": "Qt3DExtras::QMorphPhongMaterial", "signals": [{"access": "public", "arguments": [{"name": "ambient", "type": "QColor"}], "index": 0, "name": "ambientChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "diffuse", "type": "QColor"}], "index": 1, "name": "diffuseChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "specular", "type": "QColor"}], "index": 2, "name": "specularChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "shininess", "type": "float"}], "index": 3, "name": "shininessChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "interpolator", "type": "float"}], "index": 4, "name": "interpolatorChanged", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "ambient", "type": "QColor"}], "index": 5, "name": "setAmbient", "returnType": "void"}, {"access": "public", "arguments": [{"name": "diffuse", "type": "QColor"}], "index": 6, "name": "setDiffuse", "returnType": "void"}, {"access": "public", "arguments": [{"name": "specular", "type": "QColor"}], "index": 7, "name": "setSpecular", "returnType": "void"}, {"access": "public", "arguments": [{"name": "shininess", "type": "float"}], "index": 8, "name": "setShininess", "returnType": "void"}, {"access": "public", "arguments": [{"name": "interpolator", "type": "float"}], "index": 9, "name": "setInterpolator", "returnType": "void"}], "superClasses": [{"access": "public", "name": "Qt3DRender::QMaterial"}]}], "inputFile": "qmorphphongmaterial.h", "outputRevision": 69}, {"classes": [{"className": "QNormalDiffuseMapAlphaMaterial", "lineNumber": 16, "object": true, "qualifiedClassName": "Qt3DExtras::QNormalDiffuseMapAlphaMaterial", "superClasses": [{"access": "public", "fullyQualifiedName": "Qt3DExtras::QNormalDiffuseMapMaterial", "name": "QNormalDiffuseMapMaterial"}]}], "inputFile": "qnormaldiffusemapalphamaterial.h", "outputRevision": 69}, {"classes": [{"className": "QNormalDiffuseMapMaterial", "lineNumber": 23, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "ambient", "notify": "ambientChanged", "read": "ambient", "required": false, "scriptable": true, "stored": true, "type": "QColor", "user": false, "write": "setAmbient"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "specular", "notify": "specularChanged", "read": "specular", "required": false, "scriptable": true, "stored": true, "type": "QColor", "user": false, "write": "setSpecular"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "diffuse", "notify": "diffuseChanged", "read": "diffuse", "required": false, "scriptable": true, "stored": true, "type": "Qt3DRender::QAbstractTexture*", "user": false, "write": "setDiffuse"}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "normal", "notify": "normalChanged", "read": "normal", "required": false, "scriptable": true, "stored": true, "type": "Qt3DRender::QAbstractTexture*", "user": false, "write": "setNormal"}, {"constant": false, "designable": true, "final": false, "index": 4, "name": "shininess", "notify": "shininessChanged", "read": "shininess", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setShininess"}, {"constant": false, "designable": true, "final": false, "index": 5, "name": "textureScale", "notify": "textureScaleChanged", "read": "textureScale", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setTextureScale"}], "qualifiedClassName": "Qt3DExtras::QNormalDiffuseMapMaterial", "signals": [{"access": "public", "arguments": [{"name": "ambient", "type": "QColor"}], "index": 0, "name": "ambientChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "diffuse", "type": "Qt3DRender::QAbstractTexture*"}], "index": 1, "name": "diffuseChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "normal", "type": "Qt3DRender::QAbstractTexture*"}], "index": 2, "name": "normalChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "specular", "type": "QColor"}], "index": 3, "name": "specularChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "shininess", "type": "float"}], "index": 4, "name": "shininessChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "textureScale", "type": "float"}], "index": 5, "name": "textureScaleChanged", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "ambient", "type": "QColor"}], "index": 6, "name": "setAmbient", "returnType": "void"}, {"access": "public", "arguments": [{"name": "specular", "type": "QColor"}], "index": 7, "name": "setSpecular", "returnType": "void"}, {"access": "public", "arguments": [{"name": "diffuse", "type": "Qt3DRender::QAbstractTexture*"}], "index": 8, "name": "setDiffuse", "returnType": "void"}, {"access": "public", "arguments": [{"name": "normal", "type": "Qt3DRender::QAbstractTexture*"}], "index": 9, "name": "setNormal", "returnType": "void"}, {"access": "public", "arguments": [{"name": "shininess", "type": "float"}], "index": 10, "name": "setShininess", "returnType": "void"}, {"access": "public", "arguments": [{"name": "textureScale", "type": "float"}], "index": 11, "name": "setTextureScale", "returnType": "void"}], "superClasses": [{"access": "public", "name": "Qt3DRender::QMaterial"}]}], "inputFile": "qnormaldiffusemapmaterial.h", "outputRevision": 69}, {"classes": [{"className": "QNormalDiffuseSpecularMapMaterial", "lineNumber": 17, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "ambient", "notify": "ambientChanged", "read": "ambient", "required": false, "scriptable": true, "stored": true, "type": "QColor", "user": false, "write": "setAmbient"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "diffuse", "notify": "diffuseChanged", "read": "diffuse", "required": false, "scriptable": true, "stored": true, "type": "Qt3DRender::QAbstractTexture*", "user": false, "write": "setDiffuse"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "normal", "notify": "normalChanged", "read": "normal", "required": false, "scriptable": true, "stored": true, "type": "Qt3DRender::QAbstractTexture*", "user": false, "write": "setNormal"}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "specular", "notify": "specularChanged", "read": "specular", "required": false, "scriptable": true, "stored": true, "type": "Qt3DRender::QAbstractTexture*", "user": false, "write": "setSpecular"}, {"constant": false, "designable": true, "final": false, "index": 4, "name": "shininess", "notify": "shininessChanged", "read": "shininess", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setShininess"}, {"constant": false, "designable": true, "final": false, "index": 5, "name": "textureScale", "notify": "textureScaleChanged", "read": "textureScale", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setTextureScale"}], "qualifiedClassName": "Qt3DExtras::QNormalDiffuseSpecularMapMaterial", "signals": [{"access": "public", "arguments": [{"name": "ambient", "type": "QColor"}], "index": 0, "name": "ambientChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "diffuse", "type": "Qt3DRender::QAbstractTexture*"}], "index": 1, "name": "diffuseChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "normal", "type": "Qt3DRender::QAbstractTexture*"}], "index": 2, "name": "normalChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "specular", "type": "Qt3DRender::QAbstractTexture*"}], "index": 3, "name": "specularChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "shininess", "type": "float"}], "index": 4, "name": "shininessChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "textureScale", "type": "float"}], "index": 5, "name": "textureScaleChanged", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "ambient", "type": "QColor"}], "index": 6, "name": "setAmbient", "returnType": "void"}, {"access": "public", "arguments": [{"name": "diffuse", "type": "Qt3DRender::QAbstractTexture*"}], "index": 7, "name": "setDiffuse", "returnType": "void"}, {"access": "public", "arguments": [{"name": "normal", "type": "Qt3DRender::QAbstractTexture*"}], "index": 8, "name": "setNormal", "returnType": "void"}, {"access": "public", "arguments": [{"name": "specular", "type": "Qt3DRender::QAbstractTexture*"}], "index": 9, "name": "setSpecular", "returnType": "void"}, {"access": "public", "arguments": [{"name": "shininess", "type": "float"}], "index": 10, "name": "setShininess", "returnType": "void"}, {"access": "public", "arguments": [{"name": "textureScale", "type": "float"}], "index": 11, "name": "setTextureScale", "returnType": "void"}], "superClasses": [{"access": "public", "name": "Qt3DRender::QMaterial"}]}], "inputFile": "qnormaldiffusespecularmapmaterial.h", "outputRevision": 69}, {"classes": [{"className": "QOrbitCameraController", "lineNumber": 15, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "zoomInLimit", "notify": "zoomInLimitChanged", "read": "zoomInLimit", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setZoomInLimit"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "upVector", "notify": "upVectorChanged", "read": "upVector", "required": false, "revision": 1543, "scriptable": true, "stored": true, "type": "QVector3D", "user": false, "write": "setUpVector"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "inverseXTranslate", "notify": "inverseXTranslateChanged", "read": "inverseXTranslate", "required": false, "revision": 1543, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setInverseXTranslate"}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "inverseYTranslate", "notify": "inverseYTranslateChanged", "read": "inverseYTranslate", "required": false, "revision": 1543, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setInverseYTranslate"}, {"constant": false, "designable": true, "final": false, "index": 4, "name": "inversePan", "notify": "inversePanChanged", "read": "inversePan", "required": false, "revision": 1543, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setInversePan"}, {"constant": false, "designable": true, "final": false, "index": 5, "name": "inverseTilt", "notify": "inverseTiltChanged", "read": "inverseTilt", "required": false, "revision": 1543, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setInverseTilt"}, {"constant": false, "designable": true, "final": false, "index": 6, "name": "zoomTranslateViewCenter", "notify": "zoomTranslateViewCenterChanged", "read": "zoomTranslateViewCenter", "required": false, "revision": 1543, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setZoomTranslateViewCenter"}], "qualifiedClassName": "Qt3DExtras::QOrbitCameraController", "signals": [{"access": "public", "index": 0, "name": "zoomInLimitChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "upVector", "type": "QVector3D"}], "index": 1, "name": "upVectorChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "isInverse", "type": "bool"}], "index": 2, "name": "inverseXTranslateChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "isInverse", "type": "bool"}], "index": 3, "name": "inverseYTranslateChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "isInverse", "type": "bool"}], "index": 4, "name": "inversePanChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "isInverse", "type": "bool"}], "index": 5, "name": "inverseTiltChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "isTranslate", "type": "bool"}], "index": 6, "name": "zoomTranslateViewCenterChanged", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "zoomInLimit", "type": "float"}], "index": 7, "name": "setZoomInLimit", "returnType": "void"}, {"access": "public", "arguments": [{"name": "upVector", "type": "QVector3D"}], "index": 8, "name": "setUpVector", "returnType": "void"}, {"access": "public", "arguments": [{"name": "isInverse", "type": "bool"}], "index": 9, "name": "setInverseXTranslate", "returnType": "void"}, {"access": "public", "arguments": [{"name": "isInverse", "type": "bool"}], "index": 10, "name": "setInverseYTranslate", "returnType": "void"}, {"access": "public", "arguments": [{"name": "isInverse", "type": "bool"}], "index": 11, "name": "setInversePan", "returnType": "void"}, {"access": "public", "arguments": [{"name": "isInverse", "type": "bool"}], "index": 12, "name": "setInverseTilt", "returnType": "void"}, {"access": "public", "arguments": [{"name": "isTranslate", "type": "bool"}], "index": 13, "name": "setZoomTranslateViewCenter", "returnType": "void"}], "superClasses": [{"access": "public", "fullyQualifiedName": "Qt3DExtras::QAbstractCameraController", "name": "QAbstractCameraController"}]}], "inputFile": "qorbitcameracontroller.h", "outputRevision": 69}, {"classes": [{"className": "QPerVertexColorMaterial", "lineNumber": 17, "object": true, "qualifiedClassName": "Qt3DExtras::QPerVertexColorMaterial", "superClasses": [{"access": "public", "name": "Qt3DRender::QMaterial"}]}], "inputFile": "qpervertexcolormaterial.h", "outputRevision": 69}, {"classes": [{"className": "QPhongAlphaMaterial", "lineNumber": 19, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "ambient", "notify": "ambientChanged", "read": "ambient", "required": false, "scriptable": true, "stored": true, "type": "QColor", "user": false, "write": "setAmbient"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "diffuse", "notify": "diffuseChanged", "read": "diffuse", "required": false, "scriptable": true, "stored": true, "type": "QColor", "user": false, "write": "setDiffuse"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "specular", "notify": "specularChanged", "read": "specular", "required": false, "scriptable": true, "stored": true, "type": "QColor", "user": false, "write": "setSpecular"}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "shininess", "notify": "shininessChanged", "read": "shininess", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setShininess"}, {"constant": false, "designable": true, "final": false, "index": 4, "name": "alpha", "notify": "alphaChanged", "read": "alpha", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "<PERSON><PERSON><PERSON><PERSON>"}, {"constant": false, "designable": true, "final": false, "index": 5, "name": "sourceRgbArg", "notify": "sourceRgbArgChanged", "read": "sourceRgbArg", "required": false, "scriptable": true, "stored": true, "type": "Qt3DRender::QBlendEquationArguments::Blending", "user": false, "write": "setSourceRgbArg"}, {"constant": false, "designable": true, "final": false, "index": 6, "name": "destinationRgbArg", "notify": "destinationRgbArgChanged", "read": "destinationRgbArg", "required": false, "scriptable": true, "stored": true, "type": "Qt3DRender::QBlendEquationArguments::Blending", "user": false, "write": "setDestinationRgbArg"}, {"constant": false, "designable": true, "final": false, "index": 7, "name": "sourceAlphaArg", "notify": "sourceAlphaArgChanged", "read": "sourceAlphaArg", "required": false, "scriptable": true, "stored": true, "type": "Qt3DRender::QBlendEquationArguments::Blending", "user": false, "write": "setSourceAlphaArg"}, {"constant": false, "designable": true, "final": false, "index": 8, "name": "destinationAlphaArg", "notify": "destinationAlphaArgChanged", "read": "destinationAlphaArg", "required": false, "scriptable": true, "stored": true, "type": "Qt3DRender::QBlendEquationArguments::Blending", "user": false, "write": "setDestinationAlphaArg"}, {"constant": false, "designable": true, "final": false, "index": 9, "name": "blendFunctionArg", "notify": "blendFunctionArgChanged", "read": "blendFunctionArg", "required": false, "scriptable": true, "stored": true, "type": "Qt3DRender::QBlendEquation::BlendFunction", "user": false, "write": "setBlendFunctionArg"}], "qualifiedClassName": "Qt3DExtras::QPhongAlphaMaterial", "signals": [{"access": "public", "arguments": [{"name": "ambient", "type": "QColor"}], "index": 0, "name": "ambientChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "diffuse", "type": "QColor"}], "index": 1, "name": "diffuseChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "specular", "type": "QColor"}], "index": 2, "name": "specularChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "shininess", "type": "float"}], "index": 3, "name": "shininessChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "alpha", "type": "float"}], "index": 4, "name": "alphaChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "sourceRgbArg", "type": "Qt3DRender::QBlendEquationArguments::Blending"}], "index": 5, "name": "sourceRgbArgChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "destinationRgbArg", "type": "Qt3DRender::QBlendEquationArguments::Blending"}], "index": 6, "name": "destinationRgbArgChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "sourceAlphaArg", "type": "Qt3DRender::QBlendEquationArguments::Blending"}], "index": 7, "name": "sourceAlphaArgChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "destinationAlphaArg", "type": "Qt3DRender::QBlendEquationArguments::Blending"}], "index": 8, "name": "destinationAlphaArgChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "blendFunctionArg", "type": "Qt3DRender::QBlendEquation::BlendFunction"}], "index": 9, "name": "blendFunctionArgChanged", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "ambient", "type": "QColor"}], "index": 10, "name": "setAmbient", "returnType": "void"}, {"access": "public", "arguments": [{"name": "diffuse", "type": "QColor"}], "index": 11, "name": "setDiffuse", "returnType": "void"}, {"access": "public", "arguments": [{"name": "specular", "type": "QColor"}], "index": 12, "name": "setSpecular", "returnType": "void"}, {"access": "public", "arguments": [{"name": "shininess", "type": "float"}], "index": 13, "name": "setShininess", "returnType": "void"}, {"access": "public", "arguments": [{"name": "alpha", "type": "float"}], "index": 14, "name": "<PERSON><PERSON><PERSON><PERSON>", "returnType": "void"}, {"access": "public", "arguments": [{"name": "sourceRgbArg", "type": "Qt3DRender::QBlendEquationArguments::Blending"}], "index": 15, "name": "setSourceRgbArg", "returnType": "void"}, {"access": "public", "arguments": [{"name": "destinationRgbArg", "type": "Qt3DRender::QBlendEquationArguments::Blending"}], "index": 16, "name": "setDestinationRgbArg", "returnType": "void"}, {"access": "public", "arguments": [{"name": "sourceAlphaArg", "type": "Qt3DRender::QBlendEquationArguments::Blending"}], "index": 17, "name": "setSourceAlphaArg", "returnType": "void"}, {"access": "public", "arguments": [{"name": "destinationAlphaArg", "type": "Qt3DRender::QBlendEquationArguments::Blending"}], "index": 18, "name": "setDestinationAlphaArg", "returnType": "void"}, {"access": "public", "arguments": [{"name": "blendFunctionArg", "type": "Qt3DRender::QBlendEquation::BlendFunction"}], "index": 19, "name": "setBlendFunctionArg", "returnType": "void"}], "superClasses": [{"access": "public", "name": "Qt3DRender::QMaterial"}]}], "inputFile": "qphongalphamaterial.h", "outputRevision": 69}, {"classes": [{"className": "QPhongMaterial", "lineNumber": 17, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "ambient", "notify": "ambientChanged", "read": "ambient", "required": false, "scriptable": true, "stored": true, "type": "QColor", "user": false, "write": "setAmbient"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "diffuse", "notify": "diffuseChanged", "read": "diffuse", "required": false, "scriptable": true, "stored": true, "type": "QColor", "user": false, "write": "setDiffuse"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "specular", "notify": "specularChanged", "read": "specular", "required": false, "scriptable": true, "stored": true, "type": "QColor", "user": false, "write": "setSpecular"}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "shininess", "notify": "shininessChanged", "read": "shininess", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setShininess"}], "qualifiedClassName": "Qt3DExtras::QPhongMaterial", "signals": [{"access": "public", "arguments": [{"name": "ambient", "type": "QColor"}], "index": 0, "name": "ambientChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "diffuse", "type": "QColor"}], "index": 1, "name": "diffuseChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "specular", "type": "QColor"}], "index": 2, "name": "specularChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "shininess", "type": "float"}], "index": 3, "name": "shininessChanged", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "ambient", "type": "QColor"}], "index": 4, "name": "setAmbient", "returnType": "void"}, {"access": "public", "arguments": [{"name": "diffuse", "type": "QColor"}], "index": 5, "name": "setDiffuse", "returnType": "void"}, {"access": "public", "arguments": [{"name": "specular", "type": "QColor"}], "index": 6, "name": "setSpecular", "returnType": "void"}, {"access": "public", "arguments": [{"name": "shininess", "type": "float"}], "index": 7, "name": "setShininess", "returnType": "void"}], "superClasses": [{"access": "public", "name": "Qt3DRender::QMaterial"}]}], "inputFile": "qphongmaterial.h", "outputRevision": 69}, {"classes": [{"className": "QPlaneGeometry", "lineNumber": 23, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "width", "notify": "widthChanged", "read": "width", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "<PERSON><PERSON><PERSON><PERSON>"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "height", "notify": "heightChanged", "read": "height", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setHeight"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "resolution", "notify": "resolutionChanged", "read": "resolution", "required": false, "scriptable": true, "stored": true, "type": "QSize", "user": false, "write": "setResolution"}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "mirrored", "notify": "mirroredChanged", "read": "mirrored", "required": false, "revision": 65289, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "set<PERSON><PERSON><PERSON><PERSON>"}, {"constant": true, "designable": true, "final": false, "index": 4, "name": "positionAttribute", "read": "positionAttribute", "required": false, "scriptable": true, "stored": true, "type": "Qt3DCore::QAttribute*", "user": false}, {"constant": true, "designable": true, "final": false, "index": 5, "name": "normalAttribute", "read": "normalAttribute", "required": false, "scriptable": true, "stored": true, "type": "Qt3DCore::QAttribute*", "user": false}, {"constant": true, "designable": true, "final": false, "index": 6, "name": "texCoordAttribute", "read": "texCoordAttribute", "required": false, "scriptable": true, "stored": true, "type": "Qt3DCore::QAttribute*", "user": false}, {"constant": true, "designable": true, "final": false, "index": 7, "name": "tangentAttribute", "read": "tangentAttribute", "required": false, "scriptable": true, "stored": true, "type": "Qt3DCore::QAttribute*", "user": false}, {"constant": true, "designable": true, "final": false, "index": 8, "name": "indexAttribute", "read": "indexAttribute", "required": false, "scriptable": true, "stored": true, "type": "Qt3DCore::QAttribute*", "user": false}], "qualifiedClassName": "Qt3DExtras::QPlaneGeometry", "signals": [{"access": "public", "arguments": [{"name": "resolution", "type": "QSize"}], "index": 0, "name": "resolutionChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "width", "type": "float"}], "index": 1, "name": "widthChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "height", "type": "float"}], "index": 2, "name": "heightChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "mirrored", "type": "bool"}], "index": 3, "name": "mirroredChanged", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "resolution", "type": "QSize"}], "index": 4, "name": "setResolution", "returnType": "void"}, {"access": "public", "arguments": [{"name": "width", "type": "float"}], "index": 5, "name": "<PERSON><PERSON><PERSON><PERSON>", "returnType": "void"}, {"access": "public", "arguments": [{"name": "height", "type": "float"}], "index": 6, "name": "setHeight", "returnType": "void"}, {"access": "public", "arguments": [{"name": "mirrored", "type": "bool"}], "index": 7, "name": "set<PERSON><PERSON><PERSON><PERSON>", "returnType": "void"}], "superClasses": [{"access": "public", "name": "Qt3DCore::QGeometry"}]}], "inputFile": "qplanegeometry.h", "outputRevision": 69}, {"classes": [{"className": "QPlaneGeometryView", "lineNumber": 15, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "width", "notify": "widthChanged", "read": "width", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "<PERSON><PERSON><PERSON><PERSON>"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "height", "notify": "heightChanged", "read": "height", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setHeight"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "meshResolution", "notify": "meshResolutionChanged", "read": "meshResolution", "required": false, "scriptable": true, "stored": true, "type": "QSize", "user": false, "write": "setMeshResolution"}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "mirrored", "notify": "mirroredChanged", "read": "mirrored", "required": false, "revision": 65289, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "set<PERSON><PERSON><PERSON><PERSON>"}], "qualifiedClassName": "Qt3DExtras::QPlaneGeometryView", "signals": [{"access": "public", "arguments": [{"name": "meshResolution", "type": "QSize"}], "index": 0, "name": "meshResolutionChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "width", "type": "float"}], "index": 1, "name": "widthChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "height", "type": "float"}], "index": 2, "name": "heightChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "mirrored", "type": "bool"}], "index": 3, "name": "mirroredChanged", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "width", "type": "float"}], "index": 4, "name": "<PERSON><PERSON><PERSON><PERSON>", "returnType": "void"}, {"access": "public", "arguments": [{"name": "height", "type": "float"}], "index": 5, "name": "setHeight", "returnType": "void"}, {"access": "public", "arguments": [{"name": "resolution", "type": "QSize"}], "index": 6, "name": "setMeshResolution", "returnType": "void"}, {"access": "public", "arguments": [{"name": "mirrored", "type": "bool"}], "index": 7, "name": "set<PERSON><PERSON><PERSON><PERSON>", "returnType": "void"}], "superClasses": [{"access": "public", "name": "Qt3DCore::QGeometryView"}]}], "inputFile": "qplanegeometryview.h", "outputRevision": 69}, {"classes": [{"className": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "lineNumber": 15, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "width", "notify": "widthChanged", "read": "width", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "<PERSON><PERSON><PERSON><PERSON>"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "height", "notify": "heightChanged", "read": "height", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setHeight"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "meshResolution", "notify": "meshResolutionChanged", "read": "meshResolution", "required": false, "scriptable": true, "stored": true, "type": "QSize", "user": false, "write": "setMeshResolution"}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "mirrored", "notify": "mirroredChanged", "read": "mirrored", "required": false, "revision": 65289, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "set<PERSON><PERSON><PERSON><PERSON>"}], "qualifiedClassName": "Qt3DExtras::Q<PERSON><PERSON>Mesh", "signals": [{"access": "public", "arguments": [{"name": "meshResolution", "type": "QSize"}], "index": 0, "name": "meshResolutionChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "width", "type": "float"}], "index": 1, "name": "widthChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "height", "type": "float"}], "index": 2, "name": "heightChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "mirrored", "type": "bool"}], "index": 3, "name": "mirroredChanged", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "width", "type": "float"}], "index": 4, "name": "<PERSON><PERSON><PERSON><PERSON>", "returnType": "void"}, {"access": "public", "arguments": [{"name": "height", "type": "float"}], "index": 5, "name": "setHeight", "returnType": "void"}, {"access": "public", "arguments": [{"name": "resolution", "type": "QSize"}], "index": 6, "name": "setMeshResolution", "returnType": "void"}, {"access": "public", "arguments": [{"name": "mirrored", "type": "bool"}], "index": 7, "name": "set<PERSON><PERSON><PERSON><PERSON>", "returnType": "void"}], "superClasses": [{"access": "public", "name": "Qt3DRender::QGeo<PERSON><PERSON><PERSON><PERSON>"}]}], "inputFile": "qplanemesh.h", "outputRevision": 69}, {"classes": [{"className": "QSkyboxEntity", "lineNumber": 16, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "baseName", "notify": "baseNameChanged", "read": "baseName", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "setBaseName"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "extension", "notify": "extensionChanged", "read": "extension", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "setExtension"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "gammaCorrect", "notify": "gammaCorrectEnabledChanged", "read": "isGammaCorrectEnabled", "required": false, "revision": 65289, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setGammaCorrectEnabled"}], "qualifiedClassName": "Qt3DExtras::QSkyboxEntity", "signals": [{"access": "public", "arguments": [{"name": "path", "type": "QString"}], "index": 0, "name": "baseNameChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "extension", "type": "QString"}], "index": 1, "name": "extensionChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "enabled", "type": "bool"}], "index": 2, "name": "gammaCorrectEnabledChanged", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "path", "type": "QString"}], "index": 3, "name": "setBaseName", "returnType": "void"}, {"access": "public", "arguments": [{"name": "extension", "type": "QString"}], "index": 4, "name": "setExtension", "returnType": "void"}, {"access": "public", "arguments": [{"name": "enabled", "type": "bool"}], "index": 5, "name": "setGammaCorrectEnabled", "returnType": "void"}], "superClasses": [{"access": "public", "name": "Qt3DCore::QEntity"}]}], "inputFile": "qskyboxentity.h", "outputRevision": 69}, {"classes": [{"className": "QSphereGeometry", "lineNumber": 22, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "rings", "notify": "ringsChanged", "read": "rings", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setRings"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "slices", "notify": "slicesChanged", "read": "slices", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setSlices"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "radius", "notify": "radiusChanged", "read": "radius", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setRadius"}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "generateTangents", "notify": "generateTangentsChanged", "read": "generateTangents", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setGenerateTangents"}, {"constant": true, "designable": true, "final": false, "index": 4, "name": "positionAttribute", "read": "positionAttribute", "required": false, "scriptable": true, "stored": true, "type": "Qt3DCore::QAttribute*", "user": false}, {"constant": true, "designable": true, "final": false, "index": 5, "name": "normalAttribute", "read": "normalAttribute", "required": false, "scriptable": true, "stored": true, "type": "Qt3DCore::QAttribute*", "user": false}, {"constant": true, "designable": true, "final": false, "index": 6, "name": "texCoordAttribute", "read": "texCoordAttribute", "required": false, "scriptable": true, "stored": true, "type": "Qt3DCore::QAttribute*", "user": false}, {"constant": true, "designable": true, "final": false, "index": 7, "name": "tangentAttribute", "read": "tangentAttribute", "required": false, "scriptable": true, "stored": true, "type": "Qt3DCore::QAttribute*", "user": false}, {"constant": true, "designable": true, "final": false, "index": 8, "name": "indexAttribute", "read": "indexAttribute", "required": false, "scriptable": true, "stored": true, "type": "Qt3DCore::QAttribute*", "user": false}], "qualifiedClassName": "Qt3DExtras::QSphereGeometry", "signals": [{"access": "public", "arguments": [{"name": "radius", "type": "float"}], "index": 0, "name": "radiusChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "rings", "type": "int"}], "index": 1, "name": "ringsChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "slices", "type": "int"}], "index": 2, "name": "slicesChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "generateTangents", "type": "bool"}], "index": 3, "name": "generateTangentsChanged", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "rings", "type": "int"}], "index": 4, "name": "setRings", "returnType": "void"}, {"access": "public", "arguments": [{"name": "slices", "type": "int"}], "index": 5, "name": "setSlices", "returnType": "void"}, {"access": "public", "arguments": [{"name": "radius", "type": "float"}], "index": 6, "name": "setRadius", "returnType": "void"}, {"access": "public", "arguments": [{"name": "gen", "type": "bool"}], "index": 7, "name": "setGenerateTangents", "returnType": "void"}], "superClasses": [{"access": "public", "name": "Qt3DCore::QGeometry"}]}], "inputFile": "qspheregeometry.h", "outputRevision": 69}, {"classes": [{"className": "QSphereGeometryView", "lineNumber": 16, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "rings", "notify": "ringsChanged", "read": "rings", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setRings"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "slices", "notify": "slicesChanged", "read": "slices", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setSlices"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "radius", "notify": "radiusChanged", "read": "radius", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setRadius"}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "generateTangents", "notify": "generateTangentsChanged", "read": "generateTangents", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setGenerateTangents"}], "qualifiedClassName": "Qt3DExtras::QSphereGeometryView", "signals": [{"access": "public", "arguments": [{"name": "radius", "type": "float"}], "index": 0, "name": "radiusChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "rings", "type": "int"}], "index": 1, "name": "ringsChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "slices", "type": "int"}], "index": 2, "name": "slicesChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "generateTangents", "type": "bool"}], "index": 3, "name": "generateTangentsChanged", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "rings", "type": "int"}], "index": 4, "name": "setRings", "returnType": "void"}, {"access": "public", "arguments": [{"name": "slices", "type": "int"}], "index": 5, "name": "setSlices", "returnType": "void"}, {"access": "public", "arguments": [{"name": "radius", "type": "float"}], "index": 6, "name": "setRadius", "returnType": "void"}, {"access": "public", "arguments": [{"name": "gen", "type": "bool"}], "index": 7, "name": "setGenerateTangents", "returnType": "void"}], "superClasses": [{"access": "public", "name": "Qt3DCore::QGeometryView"}]}], "inputFile": "qspheregeometryview.h", "outputRevision": 69}, {"classes": [{"className": "QSphereMesh", "lineNumber": 16, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "rings", "notify": "ringsChanged", "read": "rings", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setRings"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "slices", "notify": "slicesChanged", "read": "slices", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setSlices"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "radius", "notify": "radiusChanged", "read": "radius", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setRadius"}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "generateTangents", "notify": "generateTangentsChanged", "read": "generateTangents", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setGenerateTangents"}], "qualifiedClassName": "Qt3DExtras::QSphereMesh", "signals": [{"access": "public", "arguments": [{"name": "radius", "type": "float"}], "index": 0, "name": "radiusChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "rings", "type": "int"}], "index": 1, "name": "ringsChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "slices", "type": "int"}], "index": 2, "name": "slicesChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "generateTangents", "type": "bool"}], "index": 3, "name": "generateTangentsChanged", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "rings", "type": "int"}], "index": 4, "name": "setRings", "returnType": "void"}, {"access": "public", "arguments": [{"name": "slices", "type": "int"}], "index": 5, "name": "setSlices", "returnType": "void"}, {"access": "public", "arguments": [{"name": "radius", "type": "float"}], "index": 6, "name": "setRadius", "returnType": "void"}, {"access": "public", "arguments": [{"name": "gen", "type": "bool"}], "index": 7, "name": "setGenerateTangents", "returnType": "void"}], "superClasses": [{"access": "public", "name": "Qt3DRender::QGeo<PERSON><PERSON><PERSON><PERSON>"}]}], "inputFile": "qspheremesh.h", "outputRevision": 69}, {"classes": [{"className": "QSpriteGrid", "lineNumber": 18, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "rows", "notify": "rowsChanged", "read": "rows", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setRows"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "columns", "notify": "columnsChanged", "read": "columns", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setColumns"}], "qualifiedClassName": "Qt3DExtras::QSpriteGrid", "signals": [{"access": "public", "arguments": [{"name": "rows", "type": "int"}], "index": 0, "name": "rowsChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "columns", "type": "int"}], "index": 1, "name": "columnsChanged", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "rows", "type": "int"}], "index": 2, "name": "setRows", "returnType": "void"}, {"access": "public", "arguments": [{"name": "columns", "type": "int"}], "index": 3, "name": "setColumns", "returnType": "void"}], "superClasses": [{"access": "public", "fullyQualifiedName": "Qt3DExtras::QAbstractSpriteSheet", "name": "QAbstractSpriteSheet"}]}], "inputFile": "qspritegrid.h", "outputRevision": 69}, {"classes": [{"className": "QSpriteSheet", "lineNumber": 18, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "sprites", "notify": "spritesChanged", "read": "sprites", "required": false, "scriptable": true, "stored": true, "type": "QList<QSpriteSheetItem*>", "user": false, "write": "setSprites"}], "qualifiedClassName": "Qt3DExtras::QSpriteSheet", "signals": [{"access": "public", "arguments": [{"name": "sprites", "type": "QList<QSpriteSheetItem*>"}], "index": 0, "name": "spritesChanged", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "sprites", "type": "QList<QSpriteSheetItem*>"}], "index": 1, "name": "setSprites", "returnType": "void"}], "superClasses": [{"access": "public", "fullyQualifiedName": "Qt3DExtras::QAbstractSpriteSheet", "name": "QAbstractSpriteSheet"}]}], "inputFile": "qspritesheet.h", "outputRevision": 69}, {"classes": [{"className": "QSpriteSheetItem", "lineNumber": 15, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "x", "notify": "xChanged", "read": "x", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setX"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "y", "notify": "y<PERSON><PERSON><PERSON>", "read": "y", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setY"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "width", "notify": "widthChanged", "read": "width", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "<PERSON><PERSON><PERSON><PERSON>"}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "height", "notify": "heightChanged", "read": "height", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setHeight"}], "qualifiedClassName": "Qt3DExtras::QSpriteSheetItem", "signals": [{"access": "public", "arguments": [{"name": "x", "type": "int"}], "index": 0, "name": "xChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "y", "type": "int"}], "index": 1, "name": "y<PERSON><PERSON><PERSON>", "returnType": "void"}, {"access": "public", "arguments": [{"name": "width", "type": "int"}], "index": 2, "name": "widthChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "height", "type": "int"}], "index": 3, "name": "heightChanged", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "x", "type": "int"}], "index": 4, "name": "setX", "returnType": "void"}, {"access": "public", "arguments": [{"name": "y", "type": "int"}], "index": 5, "name": "setY", "returnType": "void"}, {"access": "public", "arguments": [{"name": "width", "type": "int"}], "index": 6, "name": "<PERSON><PERSON><PERSON><PERSON>", "returnType": "void"}, {"access": "public", "arguments": [{"name": "height", "type": "int"}], "index": 7, "name": "setHeight", "returnType": "void"}], "superClasses": [{"access": "public", "name": "Qt3DCore::QNode"}]}], "inputFile": "qspritesheetitem.h", "outputRevision": 69}, {"classes": [{"className": "Qt3DWindow", "lineNumber": 43, "object": true, "qualifiedClassName": "Qt3DExtras::Qt3DWindow", "superClasses": [{"access": "public", "name": "QWindow"}]}], "inputFile": "qt3dwindow.h", "outputRevision": 69}, {"classes": [{"className": "QText2DEntity", "lineNumber": 19, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "font", "notify": "fontChanged", "read": "font", "required": false, "scriptable": true, "stored": true, "type": "QFont", "user": false, "write": "setFont"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "text", "notify": "textChanged", "read": "text", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "setText"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "color", "notify": "colorChanged", "read": "color", "required": false, "scriptable": true, "stored": true, "type": "QColor", "user": false, "write": "setColor"}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "width", "notify": "widthChanged", "read": "width", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "<PERSON><PERSON><PERSON><PERSON>"}, {"constant": false, "designable": true, "final": false, "index": 4, "name": "height", "notify": "heightChanged", "read": "height", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setHeight"}, {"constant": false, "designable": true, "final": false, "index": 5, "name": "alignment", "read": "alignment", "required": false, "scriptable": true, "stored": true, "type": "Qt::Alignment", "user": false, "write": "setAlignment"}], "qualifiedClassName": "Qt3DExtras::QText2DEntity", "signals": [{"access": "public", "arguments": [{"name": "font", "type": "QFont"}], "index": 0, "name": "fontChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "color", "type": "QColor"}], "index": 1, "name": "colorChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "text", "type": "QString"}], "index": 2, "name": "textChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "width", "type": "float"}], "index": 3, "name": "widthChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "height", "type": "float"}], "index": 4, "name": "heightChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "Qt3DCore::QEntity"}]}], "inputFile": "qtext2dentity.h", "outputRevision": 69}, {"classes": [{"className": "QText2DMaterial", "lineNumber": 29, "object": true, "qualifiedClassName": "Qt3DExtras::QText2DMaterial", "superClasses": [{"access": "public", "name": "Qt3DRender::QMaterial"}]}], "inputFile": "qtext2dmaterial_p.h", "outputRevision": 69}, {"classes": [{"className": "QTextureAtlas", "lineNumber": 28, "object": true, "qualifiedClassName": "Qt3DExtras::QTextureAtlas", "superClasses": [{"access": "public", "name": "Qt3DRender::QAbstractTexture"}]}], "inputFile": "qtextureatlas_p.h", "outputRevision": 69}, {"classes": [{"className": "QTextureMaterial", "lineNumber": 24, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "texture", "notify": "textureChanged", "read": "texture", "required": false, "scriptable": true, "stored": true, "type": "Qt3DRender::QAbstractTexture*", "user": false, "write": "setTexture"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "textureOffset", "notify": "textureOffsetChanged", "read": "textureOffset", "required": false, "scriptable": true, "stored": true, "type": "QVector2D", "user": false, "write": "setTextureOffset"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "textureTransform", "notify": "textureTransformChanged", "read": "textureTransform", "required": false, "revision": 65290, "scriptable": true, "stored": true, "type": "QMatrix3x3", "user": false, "write": "setTextureTransform"}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "alphaBlending", "notify": "alphaBlendingEnabledChanged", "read": "isAlphaBlendingEnabled", "required": false, "revision": 65291, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setAlphaBlendingEnabled"}], "qualifiedClassName": "Qt3DExtras::QTextureMaterial", "signals": [{"access": "public", "arguments": [{"name": "texture", "type": "Qt3DRender::QAbstractTexture*"}], "index": 0, "name": "textureChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "textureOffset", "type": "QVector2D"}], "index": 1, "name": "textureOffsetChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "textureTransform", "type": "QMatrix3x3"}], "index": 2, "name": "textureTransformChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "enabled", "type": "bool"}], "index": 3, "name": "alphaBlendingEnabledChanged", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "texture", "type": "Qt3DRender::QAbstractTexture*"}], "index": 4, "name": "setTexture", "returnType": "void"}, {"access": "public", "arguments": [{"name": "textureOffset", "type": "QVector2D"}], "index": 5, "name": "setTextureOffset", "returnType": "void"}, {"access": "public", "arguments": [{"name": "matrix", "type": "QMatrix3x3"}], "index": 6, "name": "setTextureTransform", "returnType": "void"}, {"access": "public", "arguments": [{"name": "enabled", "type": "bool"}], "index": 7, "name": "setAlphaBlendingEnabled", "returnType": "void"}], "superClasses": [{"access": "public", "name": "Qt3DRender::QMaterial"}]}], "inputFile": "qtexturematerial.h", "outputRevision": 69}, {"classes": [{"className": "QTorusGeometry", "lineNumber": 22, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "rings", "notify": "ringsChanged", "read": "rings", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setRings"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "slices", "notify": "slicesChanged", "read": "slices", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setSlices"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "radius", "notify": "radiusChanged", "read": "radius", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setRadius"}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "minorRadius", "notify": "minorRadius<PERSON><PERSON><PERSON>", "read": "minorRadius", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setMinorRadius"}, {"constant": true, "designable": true, "final": false, "index": 4, "name": "positionAttribute", "read": "positionAttribute", "required": false, "scriptable": true, "stored": true, "type": "Qt3DCore::QAttribute*", "user": false}, {"constant": true, "designable": true, "final": false, "index": 5, "name": "normalAttribute", "read": "normalAttribute", "required": false, "scriptable": true, "stored": true, "type": "Qt3DCore::QAttribute*", "user": false}, {"constant": true, "designable": true, "final": false, "index": 6, "name": "texCoordAttribute", "read": "texCoordAttribute", "required": false, "scriptable": true, "stored": true, "type": "Qt3DCore::QAttribute*", "user": false}, {"constant": true, "designable": true, "final": false, "index": 7, "name": "indexAttribute", "read": "indexAttribute", "required": false, "scriptable": true, "stored": true, "type": "Qt3DCore::QAttribute*", "user": false}], "qualifiedClassName": "Qt3DExtras::QTorusGeometry", "signals": [{"access": "public", "arguments": [{"name": "radius", "type": "float"}], "index": 0, "name": "radiusChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "rings", "type": "int"}], "index": 1, "name": "ringsChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "slices", "type": "int"}], "index": 2, "name": "slicesChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "minorRadius", "type": "float"}], "index": 3, "name": "minorRadius<PERSON><PERSON><PERSON>", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "rings", "type": "int"}], "index": 4, "name": "setRings", "returnType": "void"}, {"access": "public", "arguments": [{"name": "slices", "type": "int"}], "index": 5, "name": "setSlices", "returnType": "void"}, {"access": "public", "arguments": [{"name": "radius", "type": "float"}], "index": 6, "name": "setRadius", "returnType": "void"}, {"access": "public", "arguments": [{"name": "minorRadius", "type": "float"}], "index": 7, "name": "setMinorRadius", "returnType": "void"}], "superClasses": [{"access": "public", "name": "Qt3DCore::QGeometry"}]}], "inputFile": "qtorusgeometry.h", "outputRevision": 69}, {"classes": [{"className": "QTorusGeometryView", "lineNumber": 14, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "rings", "notify": "ringsChanged", "read": "rings", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setRings"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "slices", "notify": "slicesChanged", "read": "slices", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setSlices"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "radius", "notify": "radiusChanged", "read": "radius", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setRadius"}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "minorRadius", "notify": "minorRadius<PERSON><PERSON><PERSON>", "read": "minorRadius", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setMinorRadius"}], "qualifiedClassName": "Qt3DExtras::QTorusGeometryView", "signals": [{"access": "public", "arguments": [{"name": "radius", "type": "float"}], "index": 0, "name": "radiusChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "rings", "type": "int"}], "index": 1, "name": "ringsChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "slices", "type": "int"}], "index": 2, "name": "slicesChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "minorRadius", "type": "float"}], "index": 3, "name": "minorRadius<PERSON><PERSON><PERSON>", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "rings", "type": "int"}], "index": 4, "name": "setRings", "returnType": "void"}, {"access": "public", "arguments": [{"name": "slices", "type": "int"}], "index": 5, "name": "setSlices", "returnType": "void"}, {"access": "public", "arguments": [{"name": "radius", "type": "float"}], "index": 6, "name": "setRadius", "returnType": "void"}, {"access": "public", "arguments": [{"name": "minorRadius", "type": "float"}], "index": 7, "name": "setMinorRadius", "returnType": "void"}], "superClasses": [{"access": "public", "name": "Qt3DCore::QGeometryView"}]}], "inputFile": "qtorusgeometryview.h", "outputRevision": 69}, {"classes": [{"className": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "lineNumber": 14, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "rings", "notify": "ringsChanged", "read": "rings", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setRings"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "slices", "notify": "slicesChanged", "read": "slices", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setSlices"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "radius", "notify": "radiusChanged", "read": "radius", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setRadius"}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "minorRadius", "notify": "minorRadius<PERSON><PERSON><PERSON>", "read": "minorRadius", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setMinorRadius"}], "qualifiedClassName": "Qt3DExtras::QTorusMesh", "signals": [{"access": "public", "arguments": [{"name": "radius", "type": "float"}], "index": 0, "name": "radiusChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "rings", "type": "int"}], "index": 1, "name": "ringsChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "slices", "type": "int"}], "index": 2, "name": "slicesChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "minorRadius", "type": "float"}], "index": 3, "name": "minorRadius<PERSON><PERSON><PERSON>", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "rings", "type": "int"}], "index": 4, "name": "setRings", "returnType": "void"}, {"access": "public", "arguments": [{"name": "slices", "type": "int"}], "index": 5, "name": "setSlices", "returnType": "void"}, {"access": "public", "arguments": [{"name": "radius", "type": "float"}], "index": 6, "name": "setRadius", "returnType": "void"}, {"access": "public", "arguments": [{"name": "minorRadius", "type": "float"}], "index": 7, "name": "setMinorRadius", "returnType": "void"}], "superClasses": [{"access": "public", "name": "Qt3DRender::QGeo<PERSON><PERSON><PERSON><PERSON>"}]}], "inputFile": "qtorusmesh.h", "outputRevision": 69}]