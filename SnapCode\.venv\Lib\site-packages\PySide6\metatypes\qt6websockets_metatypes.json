[{"classes": [{"className": "QWebSocket", "lineNumber": 30, "object": true, "qualifiedClassName": "QWebSocket", "signals": [{"access": "public", "index": 0, "name": "aboutToClose", "returnType": "void"}, {"access": "public", "index": 1, "name": "connected", "returnType": "void"}, {"access": "public", "index": 2, "name": "disconnected", "returnType": "void"}, {"access": "public", "arguments": [{"name": "state", "type": "QAbstractSocket::SocketState"}], "index": 3, "name": "stateChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "proxy", "type": "QNetworkProxy"}, {"name": "pAuthenticator", "type": "QAuthenticator*"}], "index": 4, "name": "proxyAuthenticationRequired", "returnType": "void"}, {"access": "public", "arguments": [{"name": "authenticator", "type": "QAuthenticator*"}], "index": 5, "name": "authenticationRequired", "returnType": "void"}, {"access": "public", "index": 6, "name": "readChannelFinished", "returnType": "void"}, {"access": "public", "arguments": [{"name": "frame", "type": "QString"}, {"name": "isLastFrame", "type": "bool"}], "index": 7, "name": "textFrameReceived", "returnType": "void"}, {"access": "public", "arguments": [{"name": "frame", "type": "QByteArray"}, {"name": "isLastFrame", "type": "bool"}], "index": 8, "name": "binaryFrameReceived", "returnType": "void"}, {"access": "public", "arguments": [{"name": "message", "type": "QString"}], "index": 9, "name": "textMessageReceived", "returnType": "void"}, {"access": "public", "arguments": [{"name": "message", "type": "QByteArray"}], "index": 10, "name": "binaryMessageReceived", "returnType": "void"}, {"access": "public", "arguments": [{"name": "error", "type": "QAbstractSocket::SocketError"}], "index": 11, "name": "error", "returnType": "void"}, {"access": "public", "arguments": [{"name": "error", "type": "QAbstractSocket::SocketError"}], "index": 12, "name": "errorOccurred", "returnType": "void"}, {"access": "public", "arguments": [{"name": "elapsedTime", "type": "quint64"}, {"name": "payload", "type": "QByteArray"}], "index": 13, "name": "pong", "returnType": "void"}, {"access": "public", "arguments": [{"name": "bytes", "type": "qint64"}], "index": 14, "name": "bytes<PERSON>ritten", "returnType": "void"}, {"access": "public", "arguments": [{"name": "error", "type": "QSslError"}], "index": 15, "name": "peerVerifyError", "returnType": "void"}, {"access": "public", "arguments": [{"name": "errors", "type": "QList<QSslError>"}], "index": 16, "name": "sslErrors", "returnType": "void"}, {"access": "public", "arguments": [{"name": "authenticator", "type": "QSslPreSharedKeyAuthenticator*"}], "index": 17, "name": "preSharedKeyAuthenticationRequired", "returnType": "void"}, {"access": "public", "arguments": [{"name": "level", "type": "QSsl::<PERSON>ertLevel"}, {"name": "type", "type": "QSsl::AlertType"}, {"name": "description", "type": "QString"}], "index": 18, "name": "alertSent", "returnType": "void"}, {"access": "public", "arguments": [{"name": "level", "type": "QSsl::<PERSON>ertLevel"}, {"name": "type", "type": "QSsl::AlertType"}, {"name": "description", "type": "QString"}], "index": 19, "name": "alertReceived", "returnType": "void"}, {"access": "public", "arguments": [{"name": "error", "type": "QSslError"}], "index": 20, "name": "handshakeInterruptedOnError", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "closeCode", "type": "QWebSocketProtocol::CloseCode"}, {"name": "reason", "type": "QString"}], "index": 21, "name": "close", "returnType": "void"}, {"access": "public", "arguments": [{"name": "closeCode", "type": "QWebSocketProtocol::CloseCode"}], "index": 22, "isCloned": true, "name": "close", "returnType": "void"}, {"access": "public", "index": 23, "isCloned": true, "name": "close", "returnType": "void"}, {"access": "public", "arguments": [{"name": "url", "type": "QUrl"}], "index": 24, "name": "open", "returnType": "void"}, {"access": "public", "arguments": [{"name": "request", "type": "QNetworkRequest"}], "index": 25, "name": "open", "returnType": "void"}, {"access": "public", "arguments": [{"name": "url", "type": "QUrl"}, {"name": "options", "type": "QWebSocketHandshakeOptions"}], "index": 26, "name": "open", "returnType": "void"}, {"access": "public", "arguments": [{"name": "request", "type": "QNetworkRequest"}, {"name": "options", "type": "QWebSocketHandshakeOptions"}], "index": 27, "name": "open", "returnType": "void"}, {"access": "public", "arguments": [{"name": "payload", "type": "QByteArray"}], "index": 28, "name": "ping", "returnType": "void"}, {"access": "public", "index": 29, "isCloned": true, "name": "ping", "returnType": "void"}, {"access": "public", "index": 30, "name": "ignoreSslErrors", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qwebsocket.h", "outputRevision": 69}, {"classes": [{"className": "QWebSocketDataProcessor", "lineNumber": 34, "object": true, "qualifiedClassName": "QWebSocketDataProcessor", "signals": [{"access": "public", "arguments": [{"name": "data", "type": "QByteArray"}], "index": 0, "name": "pingReceived", "returnType": "void"}, {"access": "public", "arguments": [{"name": "data", "type": "QByteArray"}], "index": 1, "name": "pongReceived", "returnType": "void"}, {"access": "public", "arguments": [{"name": "closeCode", "type": "QWebSocketProtocol::CloseCode"}, {"name": "closeReason", "type": "QString"}], "index": 2, "name": "closeReceived", "returnType": "void"}, {"access": "public", "arguments": [{"name": "frame", "type": "QString"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "bool"}], "index": 3, "name": "textFrameReceived", "returnType": "void"}, {"access": "public", "arguments": [{"name": "frame", "type": "QByteArray"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "bool"}], "index": 4, "name": "binaryFrameReceived", "returnType": "void"}, {"access": "public", "arguments": [{"name": "message", "type": "QString"}], "index": 5, "name": "textMessageReceived", "returnType": "void"}, {"access": "public", "arguments": [{"name": "message", "type": "QByteArray"}], "index": 6, "name": "binaryMessageReceived", "returnType": "void"}, {"access": "public", "arguments": [{"name": "code", "type": "QWebSocketProtocol::CloseCode"}, {"name": "description", "type": "QString"}], "index": 7, "name": "errorEncountered", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "pIoDevice", "type": "QIODevice*"}], "index": 8, "name": "process", "returnType": "bool"}, {"access": "public", "index": 9, "name": "clear", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qwebsocketdataprocessor_p.h", "outputRevision": 69}, {"classes": [{"className": "QWebSocketHandshakeResponse", "lineNumber": 28, "object": true, "qualifiedClassName": "QWebSocketHandshakeResponse", "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qwebsockethandshakeresponse_p.h", "outputRevision": 69}, {"classes": [{"className": "QWebSocketServer", "enums": [{"isClass": false, "isFlag": false, "name": "SslMode", "values": ["SecureMode", "NonSecureMode"]}], "lineNumber": 30, "object": true, "qualifiedClassName": "QWebSocketServer", "signals": [{"access": "public", "arguments": [{"name": "socketError", "type": "QAbstractSocket::SocketError"}], "index": 0, "name": "acceptError", "returnType": "void"}, {"access": "public", "arguments": [{"name": "closeCode", "type": "QWebSocketProtocol::CloseCode"}], "index": 1, "name": "serverError", "returnType": "void"}, {"access": "public", "arguments": [{"name": "pAuthenticator", "type": "QWebSocketCorsAuthenticator*"}], "index": 2, "name": "originAuthenticationRequired", "returnType": "void"}, {"access": "public", "index": 3, "name": "newConnection", "returnType": "void"}, {"access": "public", "arguments": [{"name": "error", "type": "QSslError"}], "index": 4, "name": "peerVerifyError", "returnType": "void"}, {"access": "public", "arguments": [{"name": "errors", "type": "QList<QSslError>"}], "index": 5, "name": "sslErrors", "returnType": "void"}, {"access": "public", "arguments": [{"name": "authenticator", "type": "QSslPreSharedKeyAuthenticator*"}], "index": 6, "name": "preSharedKeyAuthenticationRequired", "returnType": "void"}, {"access": "public", "arguments": [{"name": "level", "type": "QSsl::<PERSON>ertLevel"}, {"name": "type", "type": "QSsl::AlertType"}, {"name": "description", "type": "QString"}], "index": 7, "name": "alertSent", "returnType": "void"}, {"access": "public", "arguments": [{"name": "level", "type": "QSsl::<PERSON>ertLevel"}, {"name": "type", "type": "QSsl::AlertType"}, {"name": "description", "type": "QString"}], "index": 8, "name": "alertReceived", "returnType": "void"}, {"access": "public", "arguments": [{"name": "error", "type": "QSslError"}], "index": 9, "name": "handshakeInterruptedOnError", "returnType": "void"}, {"access": "public", "index": 10, "name": "closed", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qwebsocketserver.h", "outputRevision": 69}]