import QtQuick.tooling 1.2

// This file describes the plugin-supplied types contained in the library.
// It is used for QML tooling purposes only.
//
// This file was auto-generated by qmltyperegistrar.

Module {
    Component {
        file: "private/qquick3dambientsound_p.h"
        name: "QQuick3DAmbientSound"
        accessSemantics: "reference"
        prototype: "QObject"
        exports: ["QtQuick3D.SpatialAudio/AmbientSound 6.0"]
        exportMetaObjectRevisions: [1536]
        Enum {
            name: "Loops"
            values: ["Infinite", "Once"]
        }
        Property {
            name: "source"
            type: "QUrl"
            read: "source"
            write: "setSource"
            notify: "sourceChanged"
            index: 0
        }
        Property {
            name: "volume"
            type: "float"
            read: "volume"
            write: "setVolume"
            notify: "volumeChanged"
            index: 1
        }
        Property {
            name: "loops"
            type: "int"
            read: "loops"
            write: "setLoops"
            notify: "loopsChanged"
            index: 2
        }
        Property {
            name: "autoPlay"
            type: "bool"
            read: "autoPlay"
            write: "setAutoPlay"
            notify: "autoPlayChanged"
            index: 3
        }
        Signal { name: "sourceChanged" }
        Signal { name: "volumeChanged" }
        Signal { name: "loopsChanged" }
        Signal { name: "autoPlayChanged" }
        Method { name: "play" }
        Method { name: "pause" }
        Method { name: "stop" }
    }
    Component {
        file: "private/qquick3daudioengine_p.h"
        name: "QQuick3DAudioEngine"
        accessSemantics: "reference"
        prototype: "QObject"
        exports: ["QtQuick3D.SpatialAudio/AudioEngine 6.0"]
        exportMetaObjectRevisions: [1536]
        Enum {
            name: "OutputMode"
            values: ["Surround", "Stereo", "Headphone"]
        }
        Property {
            name: "outputMode"
            type: "OutputMode"
            read: "outputMode"
            write: "setOutputMode"
            notify: "outputModeChanged"
            index: 0
        }
        Property {
            name: "outputDevice"
            type: "QAudioDevice"
            read: "outputDevice"
            write: "setOutputDevice"
            notify: "outputDeviceChanged"
            index: 1
        }
        Property {
            name: "masterVolume"
            type: "float"
            read: "masterVolume"
            write: "setMasterVolume"
            notify: "masterVolumeChanged"
            index: 2
        }
        Signal { name: "outputModeChanged" }
        Signal { name: "outputDeviceChanged" }
        Signal { name: "masterVolumeChanged" }
    }
    Component {
        file: "private/qquick3daudiolistener_p.h"
        name: "QQuick3DAudioListener"
        accessSemantics: "reference"
        prototype: "QQuick3DNode"
        exports: ["QtQuick3D.SpatialAudio/AudioListener 6.0"]
        exportMetaObjectRevisions: [1536]
        Method { name: "updatePosition" }
        Method { name: "updateRotation" }
    }
    Component {
        file: "private/qquick3daudioroom_p.h"
        name: "QQuick3DAudioRoom"
        accessSemantics: "reference"
        prototype: "QQuick3DNode"
        exports: ["QtQuick3D.SpatialAudio/AudioRoom 6.0"]
        exportMetaObjectRevisions: [1536]
        Enum {
            name: "Material"
            values: [
                "Transparent",
                "AcousticCeilingTiles",
                "BrickBare",
                "BrickPainted",
                "ConcreteBlockCoarse",
                "ConcreteBlockPainted",
                "CurtainHeavy",
                "FiberGlassInsulation",
                "GlassThin",
                "GlassThick",
                "Grass",
                "LinoleumOnConcrete",
                "Marble",
                "Metal",
                "ParquetOnConcrete",
                "PlasterRough",
                "PlasterSmooth",
                "PlywoodPanel",
                "PolishedConcreteOrTile",
                "Sheetrock",
                "WaterOrIceSurface",
                "WoodCeiling",
                "WoodPanel",
                "Uniform"
            ]
        }
        Property {
            name: "position"
            type: "QVector3D"
            read: "position"
            write: "setPosition"
            notify: "positionChanged"
            index: 0
        }
        Property {
            name: "dimensions"
            type: "QVector3D"
            read: "dimensions"
            write: "setDimensions"
            notify: "dimensionsChanged"
            index: 1
        }
        Property {
            name: "rotation"
            type: "QQuaternion"
            read: "rotation"
            write: "setRotation"
            notify: "rotationChanged"
            index: 2
        }
        Property {
            name: "leftMaterial"
            type: "Material"
            read: "leftMaterial"
            write: "setLeftMaterial"
            notify: "wallsChanged"
            index: 3
        }
        Property {
            name: "rightMaterial"
            type: "Material"
            read: "rightMaterial"
            write: "setRightMaterial"
            notify: "wallsChanged"
            index: 4
        }
        Property {
            name: "frontMaterial"
            type: "Material"
            read: "frontMaterial"
            write: "setFrontMaterial"
            notify: "wallsChanged"
            index: 5
        }
        Property {
            name: "backMaterial"
            type: "Material"
            read: "backMaterial"
            write: "setBackMaterial"
            notify: "wallsChanged"
            index: 6
        }
        Property {
            name: "floorMaterial"
            type: "Material"
            read: "floorMaterial"
            write: "setFloorMaterial"
            notify: "wallsChanged"
            index: 7
        }
        Property {
            name: "ceilingMaterial"
            type: "Material"
            read: "ceilingMaterial"
            write: "setCeilingMaterial"
            notify: "wallsChanged"
            index: 8
        }
        Property {
            name: "reflectionGain"
            type: "float"
            read: "reflectionGain"
            write: "setReflectionGain"
            notify: "reflectionGainChanged"
            index: 9
        }
        Property {
            name: "reverbGain"
            type: "float"
            read: "reverbGain"
            write: "setReverbGain"
            notify: "reverbGainChanged"
            index: 10
        }
        Property {
            name: "reverbTime"
            type: "float"
            read: "reverbTime"
            write: "setReverbTime"
            notify: "reverbTimeChanged"
            index: 11
        }
        Property {
            name: "reverbBrightness"
            type: "float"
            read: "reverbBrightness"
            write: "setReverbBrightness"
            notify: "reverbBrightnessChanged"
            index: 12
        }
        Signal { name: "positionChanged" }
        Signal { name: "dimensionsChanged" }
        Signal { name: "rotationChanged" }
        Signal { name: "wallsChanged" }
        Signal { name: "reflectionGainChanged" }
        Signal { name: "reverbGainChanged" }
        Signal { name: "reverbTimeChanged" }
        Signal { name: "reverbBrightnessChanged" }
        Method { name: "updatePosition" }
        Method { name: "updateRotation" }
    }
    Component {
        file: "private/qquick3dspatialsound_p.h"
        name: "QQuick3DSpatialSound"
        accessSemantics: "reference"
        prototype: "QQuick3DNode"
        exports: ["QtQuick3D.SpatialAudio/SpatialSound 6.0"]
        exportMetaObjectRevisions: [1536]
        Enum {
            name: "DistanceModel"
            values: ["Logarithmic", "Linear", "ManualAttenuation"]
        }
        Enum {
            name: "Loops"
            values: ["Infinite", "Once"]
        }
        Property {
            name: "source"
            type: "QUrl"
            read: "source"
            write: "setSource"
            notify: "sourceChanged"
            index: 0
        }
        Property {
            name: "volume"
            type: "float"
            read: "volume"
            write: "setVolume"
            notify: "volumeChanged"
            index: 1
        }
        Property {
            name: "distanceModel"
            type: "DistanceModel"
            read: "distanceModel"
            write: "setDistanceModel"
            notify: "distanceModelChanged"
            index: 2
        }
        Property {
            name: "size"
            type: "float"
            read: "size"
            write: "setSize"
            notify: "sizeChanged"
            index: 3
        }
        Property {
            name: "distanceCutoff"
            type: "float"
            read: "distanceCutoff"
            write: "setDistanceCutoff"
            notify: "distanceCutoffChanged"
            index: 4
        }
        Property {
            name: "manualAttenuation"
            type: "float"
            read: "manualAttenuation"
            write: "setManualAttenuation"
            notify: "manualAttenuationChanged"
            index: 5
        }
        Property {
            name: "occlusionIntensity"
            type: "float"
            read: "occlusionIntensity"
            write: "setOcclusionIntensity"
            notify: "occlusionIntensityChanged"
            index: 6
        }
        Property {
            name: "directivity"
            type: "float"
            read: "directivity"
            write: "setDirectivity"
            notify: "directivityChanged"
            index: 7
        }
        Property {
            name: "directivityOrder"
            type: "float"
            read: "directivityOrder"
            write: "setDirectivityOrder"
            notify: "directivityOrderChanged"
            index: 8
        }
        Property {
            name: "nearFieldGain"
            type: "float"
            read: "nearFieldGain"
            write: "setNearFieldGain"
            notify: "nearFieldGainChanged"
            index: 9
        }
        Property {
            name: "loops"
            type: "int"
            read: "loops"
            write: "setLoops"
            notify: "loopsChanged"
            index: 10
        }
        Property {
            name: "autoPlay"
            type: "bool"
            read: "autoPlay"
            write: "setAutoPlay"
            notify: "autoPlayChanged"
            index: 11
        }
        Signal { name: "sourceChanged" }
        Signal { name: "volumeChanged" }
        Signal { name: "distanceModelChanged" }
        Signal { name: "sizeChanged" }
        Signal { name: "distanceCutoffChanged" }
        Signal { name: "manualAttenuationChanged" }
        Signal { name: "occlusionIntensityChanged" }
        Signal { name: "directivityChanged" }
        Signal { name: "directivityOrderChanged" }
        Signal { name: "nearFieldGainChanged" }
        Signal { name: "loopsChanged" }
        Signal { name: "autoPlayChanged" }
        Method { name: "play" }
        Method { name: "pause" }
        Method { name: "stop" }
        Method { name: "updatePosition" }
        Method { name: "updateRotation" }
    }
}
