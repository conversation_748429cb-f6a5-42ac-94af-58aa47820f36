# New Placeholder Features in SnapCode

## 🚀 Quick Placeholder Creation

### 1. **Right-Click Context Menu**
- Select any text in the code editor
- Right-click to open context menu
- Choose "🏷️ Convert to Placeholder" to convert selected text
- Choose "📦 Wrap with Custom Placeholder" for custom naming
- Choose "➕ Insert Placeholder" to add a new placeholder at cursor

### 2. **Keyboard Shortcuts**
- **Ctrl+Shift+P**: Convert selected text to placeholder
- **Ctrl+Alt+P**: Insert new placeholder at cursor position
- **Ctrl+H**: Highlight all placeholders in the editor

### 3. **Quick Placeholder Button**
- Click the "🏷️ Quick Placeholder" button in the editor toolbar
- Select text first, then click the button to convert it

## 🎨 Enhanced Visual Features

### 1. **Improved Placeholder Highlighting**
- Placeholders now have:
  - Red text color (#e74c3c)
  - Light red background (#fdf2f2)
  - Bold font weight
  - Red underline for better visibility

### 2. **Fixed Text Visibility**
- Generated code preview now has proper text color (#2c3e50)
- Better contrast with light background (#f8f9fa)
- Improved selection highlighting

### 3. **Status Bar Updates**
- Shows placeholder count when parsing
- Displays created placeholder names
- Lists all found placeholders

## 📝 How to Use

### Method 1: Select and Convert
1. Type your code with regular text
2. Select the text you want to make dynamic
3. Right-click and choose "Convert to Placeholder"
4. Enter a placeholder name (auto-suggested based on selected text)

### Method 2: Keyboard Shortcuts
1. Select text in the editor
2. Press **Ctrl+Shift+P**
3. Enter placeholder name in the dialog

### Method 3: Quick Button
1. Select text in the editor
2. Click the "🏷️ Quick Placeholder" button
3. Enter placeholder name

## 🔧 Smart Placeholder Naming

The system automatically generates placeholder names from selected text:
- "component name" → "COMPONENT_NAME"
- "user-id" → "USER_ID"
- "firstName" → "FIRSTNAME"
- Special characters are converted to underscores
- Multiple underscores are collapsed to single ones
- Everything is converted to uppercase

## 💡 Tips and Tricks

1. **Visual Feedback**: After creating a placeholder, the editor will briefly highlight all placeholders
2. **Auto-parsing**: Placeholders are automatically detected and form fields are created
3. **Status Updates**: Watch the status bar for confirmation messages
4. **Context Menu**: Right-click anywhere in the editor for placeholder options
5. **Help Text**: Look for the green help text below the editor for quick tips

## 🎯 Example Workflow

1. Load or type your template code:
   ```javascript
   function createUser(userData) {
       const user = new User(userData.name);
       return user.save();
   }
   ```

2. Select "createUser" and press Ctrl+Shift+P → Enter "FUNCTION_NAME"
3. Select "userData" and convert to "PARAMETER_NAME"
4. Select "User" and convert to "CLASS_NAME"
5. Select "name" and convert to "PROPERTY_NAME"

Result:
```javascript
function {{FUNCTION_NAME}}({{PARAMETER_NAME}}) {
    const user = new {{CLASS_NAME}}({{PARAMETER_NAME}}.{{PROPERTY_NAME}});
    return user.save();
}
```

## 🔍 Troubleshooting

- **Text not visible in preview**: This has been fixed with proper text color styling
- **Placeholders not highlighting**: Press Ctrl+H to refresh highlighting
- **Context menu not showing**: Make sure you're right-clicking in the editor area
- **Shortcuts not working**: Ensure the editor has focus before using keyboard shortcuts
