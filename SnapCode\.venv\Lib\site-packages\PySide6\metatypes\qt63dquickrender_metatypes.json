[{"classes": [{"classInfos": [{"name": "QML.Element", "value": "RenderSettings"}, {"name": "QML.Foreign", "value": "Qt3DRender::QRenderSettings"}, {"name": "QML.AddedInVersion", "value": "512"}], "className": "QRenderSettingsForeign", "gadget": true, "lineNumber": 123, "qualifiedClassName": "QRenderSettingsForeign"}, {"classInfos": [{"name": "QML.Element", "value": "PickingSettings"}, {"name": "QML.Foreign", "value": "Qt3DRender::QPickingSettings"}, {"name": "QML.AddedInVersion", "value": "512"}], "className": "QPickingSettingsForeign", "gadget": true, "lineNumber": 129, "qualifiedClassName": "QPickingSettingsForeign"}, {"classInfos": [{"name": "QML.Element", "value": "RenderCapabilities"}, {"name": "QML.Foreign", "value": "Qt3DRender::QRenderCapabilities"}, {"name": "QML.Creatable", "value": "false"}, {"name": "QML.UncreatableReason", "value": "Only available as a property of RenderSettings"}, {"name": "QML.AddedInVersion", "value": "527"}], "className": "QRenderCapabilitiesForeign", "gadget": true, "lineNumber": 135, "qualifiedClassName": "QRenderCapabilitiesForeign"}, {"classInfos": [{"name": "QML.Element", "value": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "QML.Foreign", "value": "Qt3DRender::Q<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "QML.Extended", "value": "Qt3DRender::Render::Quick::Quick3DScene"}, {"name": "QML.AddedInVersion", "value": "512"}], "className": "QSceneLoaderForeign", "gadget": true, "lineNumber": 142, "qualifiedClassName": "QSceneLoaderForeign"}, {"classInfos": [{"name": "QML.Element", "value": "Effect"}, {"name": "QML.Foreign", "value": "Qt3DRender::QEffect"}, {"name": "QML.Extended", "value": "Qt3DRender::Render::Quick::Quick3DEffect"}, {"name": "QML.AddedInVersion", "value": "512"}], "className": "QEffectForeign", "gadget": true, "lineNumber": 149, "qualifiedClassName": "QEffectForeign"}, {"classInfos": [{"name": "QML.Element", "value": "Technique"}, {"name": "QML.Foreign", "value": "Qt3DRender::QTechnique"}, {"name": "QML.Extended", "value": "Qt3DRender::Render::Quick::Quick3DTechnique"}, {"name": "QML.AddedInVersion", "value": "512"}], "className": "QTechniqueForeign", "gadget": true, "lineNumber": 156, "qualifiedClassName": "QTechniqueForeign"}, {"classInfos": [{"name": "QML.Element", "value": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "QML.Foreign", "value": "Qt3DRender::<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "QML.AddedInVersion", "value": "512"}], "className": "QFilterKeyForeign", "gadget": true, "lineNumber": 163, "qualifiedClassName": "QFilterKeyForeign"}, {"classInfos": [{"name": "QML.Element", "value": "GraphicsApiFilter"}, {"name": "QML.Foreign", "value": "Qt3DRender::QGraphicsApiFilter"}, {"name": "QML.AddedInVersion", "value": "512"}], "className": "QGraphicsApiFilterForeign", "gadget": true, "lineNumber": 169, "qualifiedClassName": "QGraphicsApiFilterForeign"}, {"classInfos": [{"name": "QML.Element", "value": "QParameter"}, {"name": "QML.Foreign", "value": "Qt3DRender::QParameter"}, {"name": "QML.Creatable", "value": "false"}, {"name": "QML.UncreatableReason", "value": "Quick3D should instantiate Quick3DParameter only"}, {"name": "QML.AddedInVersion", "value": "512"}], "className": "QParameterForeign", "gadget": true, "lineNumber": 175, "qualifiedClassName": "QParameterForeign"}, {"classInfos": [{"name": "QML.Element", "value": "Material"}, {"name": "QML.Foreign", "value": "Qt3DRender::QMaterial"}, {"name": "QML.Extended", "value": "Qt3DRender::Render::Quick::Quick3DMaterial"}, {"name": "QML.AddedInVersion", "value": "512"}], "className": "QMaterialForeign", "gadget": true, "lineNumber": 182, "qualifiedClassName": "QMaterialForeign"}, {"classInfos": [{"name": "QML.Element", "value": "RenderPass"}, {"name": "QML.Foreign", "value": "Qt3DRender::QRenderPass"}, {"name": "QML.Extended", "value": "Qt3DRender::Render::Quick::Quick3DRenderPass"}, {"name": "QML.AddedInVersion", "value": "512"}], "className": "QRenderPassForeign", "gadget": true, "lineNumber": 189, "qualifiedClassName": "QRenderPassForeign"}, {"classInfos": [{"name": "QML.Element", "value": "ShaderProgram"}, {"name": "QML.Foreign", "value": "Qt3DRender::<PERSON><PERSON><PERSON><PERSON>Prog<PERSON>"}, {"name": "QML.AddedInVersion", "value": "512"}], "className": "QShaderProgramForeign", "gadget": true, "lineNumber": 196, "qualifiedClassName": "QShaderProgramForeign"}, {"classInfos": [{"name": "QML.Element", "value": "ShaderProgramBuilder"}, {"name": "QML.Foreign", "value": "Qt3DRender::QShaderProgramBuilder"}, {"name": "QML.AddedInVersion", "value": "522"}], "className": "QShaderProgramBuilderForeign", "gadget": true, "lineNumber": 202, "qualifiedClassName": "QShaderProgramBuilderForeign"}, {"classInfos": [{"name": "QML.Element", "value": "QShaderData"}, {"name": "QML.Foreign", "value": "Qt3DRender::<PERSON><PERSON><PERSON>erD<PERSON>"}, {"name": "QML.Creatable", "value": "false"}, {"name": "QML.UncreatableReason", "value": "Quick3D should instantiate Quick3DShaderData only"}, {"name": "QML.AddedInVersion", "value": "512"}], "className": "QShaderDataForeign", "gadget": true, "lineNumber": 208, "qualifiedClassName": "QShaderDataForeign"}, {"classInfos": [{"name": "QML.Element", "value": "Camera"}, {"name": "QML.Foreign", "value": "Qt3DRender::QCamera"}, {"name": "QML.AddedInVersion", "value": "512"}], "className": "QCameraForeign", "gadget": true, "lineNumber": 215, "qualifiedClassName": "QCameraForeign"}, {"classInfos": [{"name": "QML.Element", "value": "CameraLens"}, {"name": "QML.Foreign", "value": "Qt3DRender::QCameraLens"}, {"name": "QML.AddedInVersion", "value": "512"}], "className": "QCameraLensForeign", "gadget": true, "lineNumber": 221, "qualifiedClassName": "QCameraLensForeign"}, {"classInfos": [{"name": "QML.Element", "value": "WrapMode"}, {"name": "QML.Foreign", "value": "Qt3DRender::QTextureWrapMode"}, {"name": "QML.AddedInVersion", "value": "512"}], "className": "QTextureWrapModeForeign", "gadget": true, "lineNumber": 227, "qualifiedClassName": "QTextureWrapModeForeign"}, {"classInfos": [{"name": "QML.Element", "value": "Texture"}, {"name": "QML.Creatable", "value": "false"}, {"name": "QML.UncreatableReason", "value": "Texture should be created from one of the subclasses"}, {"name": "QML.Foreign", "value": "Qt3DRender::QAbstractTexture"}, {"name": "QML.AddedInVersion", "value": "512"}], "className": "QAbstractTextureForeign", "gadget": true, "lineNumber": 233, "qualifiedClassName": "QAbstractTextureForeign"}, {"classInfos": [{"name": "QML.Element", "value": "Texture1D"}, {"name": "QML.Foreign", "value": "Qt3DRender::QTexture1D"}, {"name": "QML.Extended", "value": "Qt3DRender::Render::Quick::Quick3DTextureExtension"}, {"name": "QML.AddedInVersion", "value": "512"}], "className": "QTexture1DForeign", "gadget": true, "lineNumber": 240, "qualifiedClassName": "QTexture1DForeign"}, {"classInfos": [{"name": "QML.Element", "value": "Texture1DArray"}, {"name": "QML.Foreign", "value": "Qt3DRender::QTexture1DArray"}, {"name": "QML.Extended", "value": "Qt3DRender::Render::Quick::Quick3DTextureExtension"}, {"name": "QML.AddedInVersion", "value": "512"}], "className": "QTexture1DArrayForeign", "gadget": true, "lineNumber": 247, "qualifiedClassName": "QTexture1DArrayForeign"}, {"classInfos": [{"name": "QML.Element", "value": "Texture2D"}, {"name": "QML.Foreign", "value": "Qt3DRender::QTexture2D"}, {"name": "QML.Extended", "value": "Qt3DRender::Render::Quick::Quick3DTextureExtension"}, {"name": "QML.AddedInVersion", "value": "512"}], "className": "QTexture2DForeign", "gadget": true, "lineNumber": 254, "qualifiedClassName": "QTexture2DForeign"}, {"classInfos": [{"name": "QML.Element", "value": "Texture2DArray"}, {"name": "QML.Foreign", "value": "Qt3DRender::QTexture2DArray"}, {"name": "QML.Extended", "value": "Qt3DRender::Render::Quick::Quick3DTextureExtension"}, {"name": "QML.AddedInVersion", "value": "512"}], "className": "QTexture2DArrayForeign", "gadget": true, "lineNumber": 261, "qualifiedClassName": "QTexture2DArrayForeign"}, {"classInfos": [{"name": "QML.Element", "value": "Texture3D"}, {"name": "QML.Foreign", "value": "Qt3DRender::QTexture3D"}, {"name": "QML.Extended", "value": "Qt3DRender::Render::Quick::Quick3DTextureExtension"}, {"name": "QML.AddedInVersion", "value": "512"}], "className": "QTexture3DForeign", "gadget": true, "lineNumber": 268, "qualifiedClassName": "QTexture3DForeign"}, {"classInfos": [{"name": "QML.Element", "value": "TextureCubeMap"}, {"name": "QML.Foreign", "value": "Qt3DRender::QTextureCubeMap"}, {"name": "QML.Extended", "value": "Qt3DRender::Render::Quick::Quick3DTextureExtension"}, {"name": "QML.AddedInVersion", "value": "512"}], "className": "QTextureCubeMapForeign", "gadget": true, "lineNumber": 275, "qualifiedClassName": "QTextureCubeMapForeign"}, {"classInfos": [{"name": "QML.Element", "value": "TextureCubeMapArray"}, {"name": "QML.Foreign", "value": "Qt3DRender::QTextureCubeMapArray"}, {"name": "QML.Extended", "value": "Qt3DRender::Render::Quick::Quick3DTextureExtension"}, {"name": "QML.AddedInVersion", "value": "512"}], "className": "QTextureCubeMapArrayForeign", "gadget": true, "lineNumber": 282, "qualifiedClassName": "QTextureCubeMapArrayForeign"}, {"classInfos": [{"name": "QML.Element", "value": "Texture2DMultisample"}, {"name": "QML.Foreign", "value": "Qt3DRender::QTexture2DMultisample"}, {"name": "QML.Extended", "value": "Qt3DRender::Render::Quick::Quick3DTextureExtension"}, {"name": "QML.AddedInVersion", "value": "512"}], "className": "QTexture2DMultisampleForeign", "gadget": true, "lineNumber": 289, "qualifiedClassName": "QTexture2DMultisampleForeign"}, {"classInfos": [{"name": "QML.Element", "value": "Texture2DMultisampleArray"}, {"name": "QML.Foreign", "value": "Qt3DRender::QTexture2DMultisampleArray"}, {"name": "QML.Extended", "value": "Qt3DRender::Render::Quick::Quick3DTextureExtension"}, {"name": "QML.AddedInVersion", "value": "512"}], "className": "QTexture2DMultisampleArrayForeign", "gadget": true, "lineNumber": 296, "qualifiedClassName": "QTexture2DMultisampleArrayForeign"}, {"classInfos": [{"name": "QML.Element", "value": "TextureRectangle"}, {"name": "QML.Foreign", "value": "Qt3DRender::QTextureRectangle"}, {"name": "QML.Extended", "value": "Qt3DRender::Render::Quick::Quick3DTextureExtension"}, {"name": "QML.AddedInVersion", "value": "512"}], "className": "QTextureRectangleForeign", "gadget": true, "lineNumber": 303, "qualifiedClassName": "QTextureRectangleForeign"}, {"classInfos": [{"name": "QML.Element", "value": "TextureBuffer"}, {"name": "QML.Foreign", "value": "Qt3DRender::QTextureBuffer"}, {"name": "QML.Extended", "value": "Qt3DRender::Render::Quick::Quick3DTextureExtension"}, {"name": "QML.AddedInVersion", "value": "512"}], "className": "QTextureBufferForeign", "gadget": true, "lineNumber": 310, "qualifiedClassName": "QTextureBufferForeign"}, {"classInfos": [{"name": "QML.Element", "value": "TextureLoader"}, {"name": "QML.Foreign", "value": "Qt3DRender::QTextureLoader"}, {"name": "QML.Extended", "value": "Qt3DRender::Render::Quick::Quick3DTextureExtension"}, {"name": "QML.AddedInVersion", "value": "512"}], "className": "QTextureLoaderForeign", "gadget": true, "lineNumber": 317, "qualifiedClassName": "QTextureLoaderForeign"}, {"classInfos": [{"name": "QML.Element", "value": "QAbstractTextureImage"}, {"name": "QML.Creatable", "value": "false"}, {"name": "QML.UncreatableReason", "value": "QAbstractTextureImage is abstract"}, {"name": "QML.Foreign", "value": "Qt3DRender::QAbstractTextureImage"}, {"name": "QML.AddedInVersion", "value": "512"}], "className": "QAbstractTextureImageForeign", "gadget": true, "lineNumber": 324, "qualifiedClassName": "QAbstractTextureImageForeign"}, {"classInfos": [{"name": "QML.Element", "value": "TextureImage"}, {"name": "QML.Foreign", "value": "Qt3DRender::QTextureImage"}, {"name": "QML.AddedInVersion", "value": "512"}], "className": "QTextureImageForeign", "gadget": true, "lineNumber": 331, "qualifiedClassName": "QTextureImageForeign"}, {"classInfos": [{"name": "QML.Element", "value": "SharedGLTexture"}, {"name": "QML.Foreign", "value": "Qt3DRender::QSharedGLTexture"}, {"name": "QML.AddedInVersion", "value": "525"}], "className": "QSharedGLTextureForeign", "gadget": true, "lineNumber": 337, "qualifiedClassName": "QSharedGLTextureForeign"}, {"classInfos": [{"name": "QML.Element", "value": "ShaderImage"}, {"name": "QML.Foreign", "value": "Qt3DRender::Q<PERSON><PERSON>erImage"}, {"name": "QML.AddedInVersion", "value": "526"}], "className": "QShaderImageForeign", "gadget": true, "lineNumber": 343, "qualifiedClassName": "QShaderImageForeign"}, {"classInfos": [{"name": "QML.Element", "value": "Geometry<PERSON><PERSON><PERSON>"}, {"name": "QML.Foreign", "value": "Qt3DRender::QGeo<PERSON><PERSON><PERSON><PERSON>"}, {"name": "QML.AddedInVersion", "value": "512"}], "className": "QGeometryRendererForeign", "gadget": true, "lineNumber": 349, "qualifiedClassName": "QGeometryRendererForeign"}, {"classInfos": [{"name": "QML.Element", "value": "LevelOfDetail"}, {"name": "QML.Foreign", "value": "Qt3DRender::QLevelOfDetail"}, {"name": "QML.AddedInVersion", "value": "521"}], "className": "QLevelOfDetailForeign", "gadget": true, "lineNumber": 355, "qualifiedClassName": "QLevelOfDetailForeign"}, {"classInfos": [{"name": "QML.Element", "value": "LevelOfDetailSwitch"}, {"name": "QML.Foreign", "value": "Qt3DRender::QLevelOfDetailSwitch"}, {"name": "QML.AddedInVersion", "value": "521"}], "className": "QLevelOfDetailSwitchForeign", "gadget": true, "lineNumber": 361, "qualifiedClassName": "QLevelOfDetailSwitchForeign"}, {"classInfos": [{"name": "QML.Element", "value": "levelOfDetailBoundingSphere"}, {"name": "QML.Foreign", "value": "Qt3DRender::QLevelOfDetailBoundingSphere"}, {"name": "QML.AddedInVersion", "value": "1544"}], "className": "QLevelOfDetailBoundingSphereForeign", "gadget": true, "lineNumber": 367, "qualifiedClassName": "QLevelOfDetailBoundingSphereForeign"}, {"classInfos": [{"name": "QML.Element", "value": "<PERSON><PERSON>"}, {"name": "QML.Foreign", "value": "Qt3DRender::<PERSON><PERSON><PERSON>"}, {"name": "QML.AddedInVersion", "value": "512"}], "className": "QMeshForeign", "gadget": true, "lineNumber": 373, "qualifiedClassName": "QMeshForeign"}, {"classInfos": [{"name": "QML.Element", "value": "ObjectPicker"}, {"name": "QML.Foreign", "value": "Qt3DRender::QObjectPicker"}, {"name": "QML.AddedInVersion", "value": "512"}], "className": "QObjectPickerForeign", "gadget": true, "lineNumber": 379, "qualifiedClassName": "QObjectPickerForeign"}, {"classInfos": [{"name": "QML.Element", "value": "PickEvent"}, {"name": "QML.Creatable", "value": "false"}, {"name": "QML.UncreatableReason", "value": "Events cannot be created"}, {"name": "QML.Foreign", "value": "Qt3DRender::QPickEvent"}, {"name": "QML.AddedInVersion", "value": "512"}], "className": "QPickEventForeign", "gadget": true, "lineNumber": 385, "qualifiedClassName": "QPickEventForeign"}, {"classInfos": [{"name": "QML.Element", "value": "anonymous"}, {"name": "QML.Foreign", "value": "Qt3DRender::QRayCasterHit"}, {"name": "QML.AddedInVersion", "value": "523"}], "className": "Quick3DRayCasterHitForeign", "gadget": true, "lineNumber": 392, "qualifiedClassName": "Quick3DRayCasterHitForeign"}, {"classInfos": [{"name": "QML.Element", "value": "anonymous"}, {"name": "QML.Foreign", "value": "Qt3DRender::QAbstractRayCaster::Hits"}, {"name": "QML.Sequence", "value": "Qt3DRender::QRayCasterHit"}, {"name": "QML.AddedInVersion", "value": "523"}], "className": "Quick3DRayCasterHitsForeign", "gadget": true, "lineNumber": 398, "qualifiedClassName": "Quick3DRayCasterHitsForeign"}, {"classInfos": [{"name": "QML.Element", "value": "PickingProxy"}, {"name": "QML.Foreign", "value": "Qt3DRender::QPickingProxy"}, {"name": "QML.AddedInVersion", "value": "528"}], "className": "QPickingProxyForeign", "gadget": true, "lineNumber": 405, "qualifiedClassName": "QPickingProxyForeign"}, {"classInfos": [{"name": "QML.Element", "value": "ComputeCommand"}, {"name": "QML.Foreign", "value": "Qt3DRender::QComputeCommand"}, {"name": "QML.AddedInVersion", "value": "512"}], "className": "QComputeCommandForeign", "gadget": true, "lineNumber": 411, "qualifiedClassName": "QComputeCommandForeign"}, {"classInfos": [{"name": "QML.Element", "value": "Layer"}, {"name": "QML.Foreign", "value": "Qt3DRender::<PERSON><PERSON><PERSON><PERSON>"}, {"name": "QML.AddedInVersion", "value": "512"}], "className": "QLayerForeign", "gadget": true, "lineNumber": 417, "qualifiedClassName": "QLayerForeign"}, {"classInfos": [{"name": "QML.Element", "value": "LayerFilter"}, {"name": "QML.Foreign", "value": "Qt3DRender::<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "QML.Extended", "value": "Qt3DRender::Render::Quick::Quick3D<PERSON><PERSON><PERSON><PERSON><PERSON>er"}, {"name": "QML.AddedInVersion", "value": "512"}], "className": "QLayerFilterForeign", "gadget": true, "lineNumber": 423, "qualifiedClassName": "QLayerFilterForeign"}, {"classInfos": [{"name": "QML.Element", "value": "Light"}, {"name": "QML.Creatable", "value": "false"}, {"name": "QML.UncreatableReason", "value": "Light is an abstract base class"}, {"name": "QML.Foreign", "value": "Qt3DRender::QAbstractLight"}, {"name": "QML.AddedInVersion", "value": "512"}], "className": "QAbstractLightForeign", "gadget": true, "lineNumber": 430, "qualifiedClassName": "QAbstractLightForeign"}, {"classInfos": [{"name": "QML.Element", "value": "PointLight"}, {"name": "QML.Foreign", "value": "Qt3DRender::QPointLight"}, {"name": "QML.AddedInVersion", "value": "512"}], "className": "QPointLightForeign", "gadget": true, "lineNumber": 437, "qualifiedClassName": "QPointLightForeign"}, {"classInfos": [{"name": "QML.Element", "value": "DirectionalLight"}, {"name": "QML.Foreign", "value": "Qt3DRender::QDirectionalLight"}, {"name": "QML.AddedInVersion", "value": "512"}], "className": "QDirectionalLightForeign", "gadget": true, "lineNumber": 443, "qualifiedClassName": "QDirectionalLightForeign"}, {"classInfos": [{"name": "QML.Element", "value": "EnvironmentLight"}, {"name": "QML.Foreign", "value": "Qt3DRender::QEnvironmentLight"}, {"name": "QML.AddedInVersion", "value": "521"}], "className": "QEnvironmentLightForeign", "gadget": true, "lineNumber": 449, "qualifiedClassName": "QEnvironmentLightForeign"}, {"classInfos": [{"name": "QML.Element", "value": "SpotLight"}, {"name": "QML.Foreign", "value": "Qt3DRender::QSpotLight"}, {"name": "QML.AddedInVersion", "value": "512"}], "className": "QSpotLightForeign", "gadget": true, "lineNumber": 455, "qualifiedClassName": "QSpotLightForeign"}, {"classInfos": [{"name": "QML.Element", "value": "CameraSelector"}, {"name": "QML.Foreign", "value": "Qt3DRender::QCameraSelector"}, {"name": "QML.Extended", "value": "Qt3DCore::Quick::Quick3DNode"}, {"name": "QML.AddedInVersion", "value": "512"}], "className": "QCameraSelectorForeign", "gadget": true, "lineNumber": 461, "qualifiedClassName": "QCameraSelectorForeign"}, {"classInfos": [{"name": "QML.Element", "value": "RenderPassFilter"}, {"name": "QML.Foreign", "value": "Qt3DRender::QR<PERSON><PERSON>assFilter"}, {"name": "QML.Extended", "value": "Qt3DRender::Render::Quick::Quick3DRenderPassFilter"}, {"name": "QML.AddedInVersion", "value": "512"}], "className": "QRenderPassFilterForeign", "gadget": true, "lineNumber": 468, "qualifiedClassName": "QRenderPassFilterForeign"}, {"classInfos": [{"name": "QML.Element", "value": "TechniqueFilter"}, {"name": "QML.Foreign", "value": "Qt3DRender::QTechniqueFilter"}, {"name": "QML.Extended", "value": "Qt3DRender::Render::Quick::Quick3DTechniqueFilter"}, {"name": "QML.AddedInVersion", "value": "512"}], "className": "QTechniqueFilterForeign", "gadget": true, "lineNumber": 475, "qualifiedClassName": "QTechniqueFilterForeign"}, {"classInfos": [{"name": "QML.Element", "value": "Viewport"}, {"name": "QML.Foreign", "value": "Qt3DRender::QViewport"}, {"name": "QML.Extended", "value": "Qt3DRender::Render::Quick::Quick3DViewport"}, {"name": "QML.AddedInVersion", "value": "512"}], "className": "QViewportForeign", "gadget": true, "lineNumber": 482, "qualifiedClassName": "QViewportForeign"}, {"classInfos": [{"name": "QML.Element", "value": "RenderTargetSelector"}, {"name": "QML.Foreign", "value": "Qt3DRender::QRenderTargetSelector"}, {"name": "QML.AddedInVersion", "value": "512"}], "className": "QRenderTargetSelectorForeign", "gadget": true, "lineNumber": 489, "qualifiedClassName": "QRenderTargetSelectorForeign"}, {"classInfos": [{"name": "QML.Element", "value": "ClearBuffers"}, {"name": "QML.Foreign", "value": "Qt3DRender::Q<PERSON><PERSON>rBuffers"}, {"name": "QML.AddedInVersion", "value": "512"}], "className": "QClearBuffersForeign", "gadget": true, "lineNumber": 495, "qualifiedClassName": "QClearBuffersForeign"}, {"classInfos": [{"name": "QML.Element", "value": "FrameGraphNode"}, {"name": "QML.Foreign", "value": "Qt3DRender::QFrameGraphNode"}, {"name": "QML.AddedInVersion", "value": "512"}], "className": "QFrameGraphNodeForeign", "gadget": true, "lineNumber": 501, "qualifiedClassName": "QFrameGraphNodeForeign"}, {"classInfos": [{"name": "QML.Element", "value": "RenderStateSet"}, {"name": "QML.Foreign", "value": "Qt3DRender::QRenderStateSet"}, {"name": "QML.Extended", "value": "Qt3DRender::Render::Quick::Quick3DStateSet"}, {"name": "QML.AddedInVersion", "value": "512"}], "className": "QRenderStateSetForeign", "gadget": true, "lineNumber": 507, "qualifiedClassName": "QRenderStateSetForeign"}, {"classInfos": [{"name": "QML.Element", "value": "NoDraw"}, {"name": "QML.Foreign", "value": "Qt3DRender::Q<PERSON>oDraw"}, {"name": "QML.AddedInVersion", "value": "512"}], "className": "QNoDrawForeign", "gadget": true, "lineNumber": 514, "qualifiedClassName": "QNoDrawForeign"}, {"classInfos": [{"name": "QML.Element", "value": "FrustumCulling"}, {"name": "QML.Foreign", "value": "Qt3DRender::Q<PERSON><PERSON><PERSON>Culling"}, {"name": "QML.AddedInVersion", "value": "512"}], "className": "QFrustumCullingForeign", "gadget": true, "lineNumber": 520, "qualifiedClassName": "QFrustumCullingForeign"}, {"classInfos": [{"name": "QML.Element", "value": "DispatchCompute"}, {"name": "QML.Foreign", "value": "Qt3DRender::QDispatchCompute"}, {"name": "QML.AddedInVersion", "value": "512"}], "className": "QDispatchComputeForeign", "gadget": true, "lineNumber": 526, "qualifiedClassName": "QDispatchComputeForeign"}, {"classInfos": [{"name": "QML.Element", "value": "RenderCapture"}, {"name": "QML.Foreign", "value": "Qt3DRender::QRenderCapture"}, {"name": "QML.AddedInVersion", "value": "513"}], "className": "QRenderCaptureForeign", "gadget": true, "lineNumber": 532, "qualifiedClassName": "QRenderCaptureForeign"}, {"classInfos": [{"name": "QML.Element", "value": "RenderCaptureReply"}, {"name": "QML.Creatable", "value": "false"}, {"name": "QML.UncreatableReason", "value": "RenderCaptureReply is only instantiated by RenderCapture"}, {"name": "QML.Foreign", "value": "Qt3DRender::QR<PERSON><PERSON><PERSON><PERSON>Reply"}, {"name": "QML.AddedInVersion", "value": "513"}], "className": "QRenderCaptureReplyForeign", "gadget": true, "lineNumber": 538, "qualifiedClassName": "QRenderCaptureReplyForeign"}, {"classInfos": [{"name": "QML.Element", "value": "BufferCapture"}, {"name": "QML.Foreign", "value": "Qt3DRender::<PERSON><PERSON><PERSON>erCap<PERSON>"}, {"name": "QML.AddedInVersion", "value": "521"}], "className": "QBufferCaptureForeign", "gadget": true, "lineNumber": 545, "qualifiedClassName": "QBufferCaptureForeign"}, {"classInfos": [{"name": "QML.Element", "value": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "QML.Foreign", "value": "Qt3DRender::<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "QML.Extended", "value": "Qt3DRender::Render::Quick::<PERSON>3<PERSON><PERSON><PERSON>B<PERSON><PERSON>"}, {"name": "QML.AddedInVersion", "value": "521"}], "className": "QMemoryBarrierForeign", "gadget": true, "lineNumber": 551, "qualifiedClassName": "QMemoryBarrierForeign"}, {"classInfos": [{"name": "QML.Element", "value": "ProximityFilter"}, {"name": "QML.Foreign", "value": "Qt3DRender::QProximityFilter"}, {"name": "QML.AddedInVersion", "value": "522"}], "className": "QProximityFilterForeign", "gadget": true, "lineNumber": 558, "qualifiedClassName": "QProximityFilterForeign"}, {"classInfos": [{"name": "QML.Element", "value": "Blit<PERSON>ramebuffer"}, {"name": "QML.Foreign", "value": "Qt3DRender::<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "QML.AddedInVersion", "value": "522"}], "className": "QBlitFramebufferForeign", "gadget": true, "lineNumber": 564, "qualifiedClassName": "QBlitFramebufferForeign"}, {"classInfos": [{"name": "QML.Element", "value": "SetFence"}, {"name": "QML.Foreign", "value": "Qt3DRender::QSetFence"}, {"name": "QML.AddedInVersion", "value": "525"}], "className": "QSetFenceForeign", "gadget": true, "lineNumber": 570, "qualifiedClassName": "QSetFenceForeign"}, {"classInfos": [{"name": "QML.Element", "value": "WaitFence"}, {"name": "QML.Foreign", "value": "Qt3DRender::QWaitFence"}, {"name": "QML.AddedInVersion", "value": "525"}], "className": "QWaitFenceForeign", "gadget": true, "lineNumber": 576, "qualifiedClassName": "QWaitFenceForeign"}, {"classInfos": [{"name": "QML.Element", "value": "NoPicking"}, {"name": "QML.Foreign", "value": "Qt3DRender::QNoPicking"}, {"name": "QML.AddedInVersion", "value": "526"}], "className": "QNoPickingForeign", "gadget": true, "lineNumber": 582, "qualifiedClassName": "QNoPickingForeign"}, {"classInfos": [{"name": "QML.Element", "value": "SubtreeEnabler"}, {"name": "QML.Foreign", "value": "Qt3DRender::QSubtreeEnabler"}, {"name": "QML.AddedInVersion", "value": "526"}], "className": "QSubtreeEnablerForeign", "gadget": true, "lineNumber": 588, "qualifiedClassName": "QSubtreeEnablerForeign"}, {"classInfos": [{"name": "QML.Element", "value": "DebugOverlay"}, {"name": "QML.Foreign", "value": "Qt3DRender::<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "QML.AddedInVersion", "value": "528"}], "className": "QDebugOverlayForeign", "gadget": true, "lineNumber": 594, "qualifiedClassName": "QDebugOverlayForeign"}, {"classInfos": [{"name": "QML.Element", "value": "RenderTargetOutput"}, {"name": "QML.Foreign", "value": "Qt3DRender::QRenderTargetOutput"}, {"name": "QML.AddedInVersion", "value": "512"}], "className": "QRenderTargetOutputForeign", "gadget": true, "lineNumber": 600, "qualifiedClassName": "QRenderTargetOutputForeign"}, {"classInfos": [{"name": "QML.Element", "value": "RenderTarget"}, {"name": "QML.Foreign", "value": "Qt3DRender::QR<PERSON><PERSON><PERSON>get"}, {"name": "QML.Extended", "value": "Qt3DRender::Render::Quick::Quick3DRenderTargetOutput"}, {"name": "QML.AddedInVersion", "value": "512"}], "className": "QRenderTargetForeign", "gadget": true, "lineNumber": 606, "qualifiedClassName": "QRenderTargetForeign"}, {"classInfos": [{"name": "QML.Element", "value": "RenderSurfaceSelector"}, {"name": "QML.Foreign", "value": "Qt3DRender::QRenderSurfaceSelector"}, {"name": "QML.AddedInVersion", "value": "512"}], "className": "QRenderSurfaceSelectorForeign", "gadget": true, "lineNumber": 613, "qualifiedClassName": "QRenderSurfaceSelectorForeign"}, {"classInfos": [{"name": "QML.Element", "value": "SortPolicy"}, {"name": "QML.Foreign", "value": "Qt3DRender::QSortPolicy"}, {"name": "QML.AddedInVersion", "value": "512"}], "className": "QSortPolicyForeign", "gadget": true, "lineNumber": 619, "qualifiedClassName": "QSortPolicyForeign"}, {"classInfos": [{"name": "QML.Element", "value": "RenderState"}, {"name": "QML.Creatable", "value": "false"}, {"name": "QML.UncreatableReason", "value": "QRenderState is a base class"}, {"name": "QML.Foreign", "value": "Qt3DRender::QRenderState"}, {"name": "QML.AddedInVersion", "value": "512"}], "className": "QRenderStateForeign", "gadget": true, "lineNumber": 625, "qualifiedClassName": "QRenderStateForeign"}, {"classInfos": [{"name": "QML.Element", "value": "BlendEquationArguments"}, {"name": "QML.Foreign", "value": "Qt3DRender::QBlendEquationArguments"}, {"name": "QML.AddedInVersion", "value": "512"}], "className": "QBlendEquationArgumentsForeign", "gadget": true, "lineNumber": 632, "qualifiedClassName": "QBlendEquationArgumentsForeign"}, {"classInfos": [{"name": "QML.Element", "value": "BlendEquation"}, {"name": "QML.Foreign", "value": "Qt3DRender::QBlendEquation"}, {"name": "QML.AddedInVersion", "value": "512"}], "className": "QBlendEquationForeign", "gadget": true, "lineNumber": 638, "qualifiedClassName": "QBlendEquationForeign"}, {"classInfos": [{"name": "QML.Element", "value": "AlphaTest"}, {"name": "QML.Foreign", "value": "Qt3DRender::QAlphaTest"}, {"name": "QML.AddedInVersion", "value": "512"}], "className": "QAlphaTestForeign", "gadget": true, "lineNumber": 644, "qualifiedClassName": "QAlphaTestForeign"}, {"classInfos": [{"name": "QML.Element", "value": "DepthRange"}, {"name": "QML.Foreign", "value": "Qt3DRender::<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "QML.AddedInVersion", "value": "526"}], "className": "QDepthRangeForeign", "gadget": true, "lineNumber": 650, "qualifiedClassName": "QDepthRangeForeign"}, {"classInfos": [{"name": "QML.Element", "value": "DepthTest"}, {"name": "QML.Foreign", "value": "Qt3DRender::QDepthTest"}, {"name": "QML.AddedInVersion", "value": "512"}], "className": "QDepthTestForeign", "gadget": true, "lineNumber": 656, "qualifiedClassName": "QDepthTestForeign"}, {"classInfos": [{"name": "QML.Element", "value": "MultiSampleAntiAliasing"}, {"name": "QML.Foreign", "value": "Qt3DRender::QMultiSampleAntiAliasing"}, {"name": "QML.AddedInVersion", "value": "512"}], "className": "QMultiSampleAntiAliasingForeign", "gadget": true, "lineNumber": 662, "qualifiedClassName": "QMultiSampleAntiAliasingForeign"}, {"classInfos": [{"name": "QML.Element", "value": "NoDepthMask"}, {"name": "QML.Foreign", "value": "Qt3DRender::QNoDepthMask"}, {"name": "QML.AddedInVersion", "value": "512"}], "className": "QNoDepthMaskForeign", "gadget": true, "lineNumber": 668, "qualifiedClassName": "QNoDepthMaskForeign"}, {"classInfos": [{"name": "QML.Element", "value": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "QML.Foreign", "value": "Qt3DRender::Q<PERSON>ullFace"}, {"name": "QML.AddedInVersion", "value": "512"}], "className": "QCullFaceForeign", "gadget": true, "lineNumber": 674, "qualifiedClassName": "QCullFaceForeign"}, {"classInfos": [{"name": "QML.Element", "value": "FrontFace"}, {"name": "QML.Foreign", "value": "Qt3DRender::QFrontFace"}, {"name": "QML.AddedInVersion", "value": "512"}], "className": "QFrontFaceForeign", "gadget": true, "lineNumber": 680, "qualifiedClassName": "QFrontFaceForeign"}, {"classInfos": [{"name": "QML.Element", "value": "StencilTestArguments"}, {"name": "QML.Foreign", "value": "Qt3DRender::QStencilTestArguments"}, {"name": "QML.Creatable", "value": "false"}, {"name": "QML.UncreatableReason", "value": "QStencilTestArguments cannot be instantiated on its own"}, {"name": "QML.AddedInVersion", "value": "512"}], "className": "QStencilTestArgumentsForeign", "gadget": true, "lineNumber": 686, "qualifiedClassName": "QStencilTestArgumentsForeign"}, {"classInfos": [{"name": "QML.Element", "value": "StencilTest"}, {"name": "QML.Foreign", "value": "Qt3DRender::QStencilTest"}, {"name": "QML.AddedInVersion", "value": "512"}], "className": "QStencilTestForeign", "gadget": true, "lineNumber": 693, "qualifiedClassName": "QStencilTestForeign"}, {"classInfos": [{"name": "QML.Element", "value": "ScissorTest"}, {"name": "QML.Foreign", "value": "Qt3DRender::QScissorTest"}, {"name": "QML.AddedInVersion", "value": "512"}], "className": "QScissorTestForeign", "gadget": true, "lineNumber": 699, "qualifiedClassName": "QScissorTestForeign"}, {"classInfos": [{"name": "QML.Element", "value": "<PERSON><PERSON><PERSON>"}, {"name": "QML.Foreign", "value": "Qt3DRender::<PERSON><PERSON><PERSON><PERSON>"}, {"name": "QML.AddedInVersion", "value": "512"}], "className": "QDitheringForeign", "gadget": true, "lineNumber": 705, "qualifiedClassName": "QDitheringForeign"}, {"classInfos": [{"name": "QML.Element", "value": "AlphaCoverage"}, {"name": "QML.Foreign", "value": "Qt3DRender::QAlphaCoverage"}, {"name": "QML.AddedInVersion", "value": "512"}], "className": "QAlphaCoverageForeign", "gadget": true, "lineNumber": 711, "qualifiedClassName": "QAlphaCoverageForeign"}, {"classInfos": [{"name": "QML.Element", "value": "PointSize"}, {"name": "QML.Foreign", "value": "Qt3DRender::QPointSize"}, {"name": "QML.AddedInVersion", "value": "512"}], "className": "QPointSizeForeign", "gadget": true, "lineNumber": 717, "qualifiedClassName": "QPointSizeForeign"}, {"classInfos": [{"name": "QML.Element", "value": "PolygonOffset"}, {"name": "QML.Foreign", "value": "Qt3DRender::QPolygonOffset"}, {"name": "QML.AddedInVersion", "value": "512"}], "className": "QPolygonOffsetForeign", "gadget": true, "lineNumber": 723, "qualifiedClassName": "QPolygonOffsetForeign"}, {"classInfos": [{"name": "QML.Element", "value": "ColorMask"}, {"name": "QML.Foreign", "value": "Qt3DRender::QColorMask"}, {"name": "QML.AddedInVersion", "value": "512"}], "className": "QColorMaskForeign", "gadget": true, "lineNumber": 729, "qualifiedClassName": "QColorMaskForeign"}, {"classInfos": [{"name": "QML.Element", "value": "ClipPlane"}, {"name": "QML.Foreign", "value": "Qt3DRender::Q<PERSON>lipPlane"}, {"name": "QML.AddedInVersion", "value": "512"}], "className": "QClipPlaneForeign", "gadget": true, "lineNumber": 735, "qualifiedClassName": "QClipPlaneForeign"}, {"classInfos": [{"name": "QML.Element", "value": "StencilOperationArguments"}, {"name": "QML.Foreign", "value": "Qt3DRender::QStencilOperationArguments"}, {"name": "QML.Creatable", "value": "false"}, {"name": "QML.UncreatableReason", "value": "StencilOperationArguments cannot be instanciated on its own"}, {"name": "QML.AddedInVersion", "value": "512"}], "className": "QStencilOperationArgumentsForeign", "gadget": true, "lineNumber": 741, "qualifiedClassName": "QStencilOperationArgumentsForeign"}, {"classInfos": [{"name": "QML.Element", "value": "SeamlessCubemap"}, {"name": "QML.Foreign", "value": "Qt3DRender::QSeamlessCubemap"}, {"name": "QML.AddedInVersion", "value": "512"}], "className": "QSeamlessCubemapForeign", "gadget": true, "lineNumber": 748, "qualifiedClassName": "QSeamlessCubemapForeign"}, {"classInfos": [{"name": "QML.Element", "value": "StencilOperation"}, {"name": "QML.Foreign", "value": "Qt3DRender::QStencilOperation"}, {"name": "QML.AddedInVersion", "value": "512"}], "className": "QStencilOperationForeign", "gadget": true, "lineNumber": 754, "qualifiedClassName": "QStencilOperationForeign"}, {"classInfos": [{"name": "QML.Element", "value": "StencilMask"}, {"name": "QML.Foreign", "value": "Qt3DRender::QStencilMask"}, {"name": "QML.AddedInVersion", "value": "512"}], "className": "QStencilMaskForeign", "gadget": true, "lineNumber": 760, "qualifiedClassName": "QStencilMaskForeign"}, {"classInfos": [{"name": "QML.Element", "value": "LineWidth"}, {"name": "QML.Foreign", "value": "Qt3DRender::<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "QML.AddedInVersion", "value": "522"}], "className": "QLineWidthForeign", "gadget": true, "lineNumber": 766, "qualifiedClassName": "QLineWidthForeign"}, {"classInfos": [{"name": "QML.Element", "value": "RasterMode"}, {"name": "QML.Foreign", "value": "Qt3DRender::QRasterMode"}, {"name": "QML.AddedInVersion", "value": "525"}], "className": "QRasterModeForeign", "gadget": true, "lineNumber": 772, "qualifiedClassName": "QRasterModeForeign"}], "inputFile": "qt3dquick3drenderforeign_p.h", "outputRevision": 69}, {"classes": [{"className": "Quick3DEffect", "lineNumber": 30, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "techniques", "read": "techniqueList", "required": false, "scriptable": true, "stored": true, "type": "QQmlListProperty<Qt3DRender::QTechnique>", "user": false}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "parameters", "read": "parameterList", "required": false, "scriptable": true, "stored": true, "type": "QQmlListProperty<Qt3DRender::QParameter>", "user": false}], "qualifiedClassName": "Qt3DRender::Render::Quick::Quick3DEffect", "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "quick3deffect_p.h", "outputRevision": 69}, {"classes": [{"className": "Quick3D<PERSON>ayer<PERSON>ilter", "lineNumber": 30, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "layers", "read": "qmlLayers", "required": false, "scriptable": true, "stored": true, "type": "QQmlListProperty<Qt3DRender::QLayer>", "user": false}], "qualifiedClassName": "Qt3DRender::Render::Quick::Quick3D<PERSON><PERSON><PERSON><PERSON><PERSON>er", "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "quick3dlayerfilter_p.h", "outputRevision": 69}, {"classes": [{"className": "Quick3DMaterial", "lineNumber": 33, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "parameters", "read": "qmlParameters", "required": false, "scriptable": true, "stored": true, "type": "QQmlListProperty<Qt3DRender::QParameter>", "user": false}], "qualifiedClassName": "Qt3DRender::Render::Quick::Quick3DMaterial", "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "quick3dmaterial_p.h", "outputRevision": 69}, {"classes": [{"className": "Quick3<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "lineNumber": 29, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "waitFor", "read": "waitFor", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setWaitFor"}], "qualifiedClassName": "Qt3DRender::Render::Quick::<PERSON>3<PERSON><PERSON><PERSON>B<PERSON><PERSON>", "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "quick3dmemorybarrier_p.h", "outputRevision": 69}, {"classes": [{"classInfos": [{"name": "QML.Element", "value": "Parameter"}, {"name": "QML.AddedInVersion", "value": "512"}], "className": "Quick3DParameter", "lineNumber": 32, "object": true, "qualifiedClassName": "Qt3DRender::Render::Quick::Quick3DParameter", "superClasses": [{"access": "public", "fullyQualifiedName": "Qt3DRender::QParameter", "name": "QParameter"}]}], "inputFile": "quick3dparameter_p.h", "outputRevision": 69}, {"classes": [{"classInfos": [{"name": "QML.Element", "value": "RayCaster"}, {"name": "QML.AddedInVersion", "value": "523"}], "className": "Quick3DRayCaster", "lineNumber": 34, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "layers", "read": "qmlLayers", "required": false, "scriptable": true, "stored": true, "type": "QQmlListProperty<Qt3DRender::QLayer>", "user": false}], "qualifiedClassName": "Qt3DRender::Render::Quick::Quick3DRayCaster", "superClasses": [{"access": "public", "fullyQualifiedName": "Qt3DRender::QRayCaster", "name": "QRayCaster"}]}], "inputFile": "quick3draycaster_p.h", "outputRevision": 69}, {"classes": [{"className": "Quick3DRenderPass", "lineNumber": 30, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "filterKeys", "read": "filterKeyList", "required": false, "scriptable": true, "stored": true, "type": "QQmlListProperty<Qt3DRender::Q<PERSON><PERSON><PERSON><PERSON><PERSON>>", "user": false}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "renderStates", "read": "renderStateList", "required": false, "scriptable": true, "stored": true, "type": "QQmlListProperty<Qt3DRender::QRenderState>", "user": false}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "parameters", "read": "parameterList", "required": false, "scriptable": true, "stored": true, "type": "QQmlListProperty<Qt3DRender::QParameter>", "user": false}], "qualifiedClassName": "Qt3DRender::Render::Quick::Quick3DRenderPass", "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "quick3drenderpass_p.h", "outputRevision": 69}, {"classes": [{"className": "Quick3DRenderPass<PERSON>ilter", "lineNumber": 31, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "matchAny", "read": "includeList", "required": false, "scriptable": true, "stored": true, "type": "QQmlListProperty<Qt3DRender::Q<PERSON><PERSON><PERSON><PERSON><PERSON>>", "user": false}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "parameters", "read": "parameterList", "required": false, "scriptable": true, "stored": true, "type": "QQmlListProperty<Qt3DRender::QParameter>", "user": false}], "qualifiedClassName": "Qt3DRender::Render::Quick::Quick3DRenderPassFilter", "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "quick3drenderpassfilter_p.h", "outputRevision": 69}, {"classes": [{"className": "Quick3DRenderTargetOutput", "lineNumber": 30, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "attachments", "read": "qmlAttachments", "required": false, "scriptable": true, "stored": true, "type": "QQmlListProperty<Qt3DRender::QRenderTargetOutput>", "user": false}], "qualifiedClassName": "Qt3DRender::Render::Quick::Quick3DRenderTargetOutput", "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "quick3drendertargetoutput_p.h", "outputRevision": 69}, {"classes": [{"className": "Quick3DScene", "lineNumber": 29, "object": true, "qualifiedClassName": "Qt3DRender::Render::Quick::Quick3DScene", "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "quick3dscene_p.h", "outputRevision": 69}, {"classes": [{"classInfos": [{"name": "QML.Element", "value": "ScreenRayCaster"}, {"name": "QML.AddedInVersion", "value": "523"}], "className": "Quick3DScreenRayCaster", "lineNumber": 33, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "layers", "read": "qmlLayers", "required": false, "scriptable": true, "stored": true, "type": "QQmlListProperty<Qt3DRender::QLayer>", "user": false}], "qualifiedClassName": "Qt3DRender::Render::Quick::Quick3DScreenRayCaster", "superClasses": [{"access": "public", "fullyQualifiedName": "Qt3DRender::QScreenRayCaster", "name": "QScreenRayCaster"}]}], "inputFile": "quick3dscreenraycaster_p.h", "outputRevision": 69}, {"classes": [{"classInfos": [{"name": "QML.Element", "value": "ShaderData"}, {"name": "QML.AddedInVersion", "value": "512"}], "className": "Quick3DShaderData", "lineNumber": 31, "object": true, "qualifiedClassName": "Qt3DRender::Render::Quick::Quick3DShaderData", "superClasses": [{"access": "public", "fullyQualifiedName": "Qt3DRender::<PERSON><PERSON><PERSON>erD<PERSON>", "name": "QShaderData"}]}], "inputFile": "quick3dshaderdata_p.h", "outputRevision": 69}, {"classes": [{"classInfos": [{"name": "QML.Element", "value": "ShaderDataArray"}, {"name": "QML.AddedInVersion", "value": "512"}, {"name": "DefaultProperty", "value": "values"}], "className": "Quick3DShaderDataArray", "lineNumber": 37, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "values", "read": "valuesList", "required": false, "scriptable": true, "stored": true, "type": "QQmlListProperty<Qt3DRender::QShaderData>", "user": false}], "qualifiedClassName": "Qt3DRender::Render::Quick::Quick3DShaderDataArray", "superClasses": [{"access": "public", "name": "Qt3DCore::QNode"}]}], "inputFile": "quick3dshaderdataarray_p.h", "outputRevision": 69}, {"classes": [{"className": "Quick3DStateSet", "lineNumber": 29, "object": true, "properties": [{"constant": true, "designable": true, "final": false, "index": 0, "name": "renderStates", "read": "renderStateList", "required": false, "scriptable": true, "stored": true, "type": "QQmlListProperty<Qt3DRender::QRenderState>", "user": false}], "qualifiedClassName": "Qt3DRender::Render::Quick::Quick3DStateSet", "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "quick3dstateset_p.h", "outputRevision": 69}, {"classes": [{"className": "Quick3DTechnique", "lineNumber": 29, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "filterKeys", "read": "filterKeyList", "required": false, "scriptable": true, "stored": true, "type": "QQmlListProperty<Qt3DRender::Q<PERSON><PERSON><PERSON><PERSON><PERSON>>", "user": false}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "renderPasses", "read": "renderPassList", "required": false, "scriptable": true, "stored": true, "type": "QQmlListProperty<Qt3DRender::QRenderPass>", "user": false}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "parameters", "read": "parameterList", "required": false, "scriptable": true, "stored": true, "type": "QQmlListProperty<Qt3DRender::QParameter>", "user": false}], "qualifiedClassName": "Qt3DRender::Render::Quick::Quick3DTechnique", "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "quick3dtechnique_p.h", "outputRevision": 69}, {"classes": [{"className": "Quick3DTechniqueFilter", "lineNumber": 31, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "matchAll", "read": "matchList", "required": false, "scriptable": true, "stored": true, "type": "QQmlListProperty<Qt3DRender::Q<PERSON><PERSON><PERSON><PERSON><PERSON>>", "user": false}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "parameters", "read": "parameterList", "required": false, "scriptable": true, "stored": true, "type": "QQmlListProperty<Qt3DRender::QParameter>", "user": false}], "qualifiedClassName": "Qt3DRender::Render::Quick::Quick3DTechniqueFilter", "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "quick3dtechniquefilter_p.h", "outputRevision": 69}, {"classes": [{"classInfos": [{"name": "DefaultProperty", "value": "textureImages"}], "className": "Quick3DTextureExtension", "lineNumber": 30, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "textureImages", "read": "textureImages", "required": false, "scriptable": true, "stored": true, "type": "QQmlListProperty<Qt3DRender::QAbstractTextureImage>", "user": false}], "qualifiedClassName": "Qt3DRender::Render::Quick::Quick3DTextureExtension", "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "quick3dtexture_p.h", "outputRevision": 69}, {"classes": [{"className": "Quick3DViewport", "lineNumber": 31, "object": true, "qualifiedClassName": "Qt3DRender::Render::Quick::Quick3DViewport", "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "quick3dviewport_p.h", "outputRevision": 69}]